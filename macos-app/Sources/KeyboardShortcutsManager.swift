import Foundation
import SwiftUI
import Carbon
import Cocoa

// MARK: - Keyboard Shortcut Models

struct KeyboardShortcut: <PERSON><PERSON>le, Identifiable {
    let id = UUID()
    let action: ShortcutAction
    let keyCode: UInt16
    let modifiers: NSEvent.ModifierFlags
    let isEnabled: Bool
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
        hasher.combine(action)
        hasher.combine(keyCode)
        hasher.combine(modifiers.rawValue)
        hasher.combine(isEnabled)
    }
    
    static func == (lhs: KeyboardShortcut, rhs: KeyboardShortcut) -> Bool {
        return lhs.id == rhs.id &&
               lhs.action == rhs.action &&
               lhs.keyCode == rhs.keyCode &&
               lhs.modifiers == rhs.modifiers &&
               lhs.isEnabled == rhs.isEnabled
    }
    
    var displayString: String {
        var components: [String] = []
        
        if modifiers.contains(.command) {
            components.append("⌘")
        }
        if modifiers.contains(.option) {
            components.append("⌥")
        }
        if modifiers.contains(.control) {
            components.append("⌃")
        }
        if modifiers.contains(.shift) {
            components.append("⇧")
        }
        
        components.append(keyCodeToString(keyCode))
        
        return components.joined()
    }
    
    private func keyCodeToString(_ keyCode: UInt16) -> String {
        switch keyCode {
        case 0: return "A"
        case 1: return "S"
        case 2: return "D"
        case 3: return "F"
        case 4: return "H"
        case 5: return "G"
        case 6: return "Z"
        case 7: return "X"
        case 8: return "C"
        case 9: return "V"
        case 11: return "B"
        case 12: return "Q"
        case 13: return "W"
        case 14: return "E"
        case 15: return "R"
        case 16: return "Y"
        case 17: return "T"
        case 18: return "1"
        case 19: return "2"
        case 20: return "3"
        case 21: return "4"
        case 22: return "6"
        case 23: return "5"
        case 24: return "="
        case 25: return "9"
        case 26: return "7"
        case 27: return "-"
        case 28: return "8"
        case 29: return "0"
        case 30: return "]"
        case 31: return "O"
        case 32: return "U"
        case 33: return "["
        case 34: return "I"
        case 35: return "P"
        case 36: return "Return"
        case 37: return "L"
        case 38: return "J"
        case 39: return "'"
        case 40: return "K"
        case 41: return ";"
        case 42: return "\\"
        case 43: return ","
        case 44: return "/"
        case 45: return "N"
        case 46: return "M"
        case 47: return "."
        case 48: return "Tab"
        case 49: return "Space"
        case 50: return "`"
        case 51: return "Delete"
        case 53: return "Escape"
        case 96: return "F5"
        case 97: return "F6"
        case 98: return "F7"
        case 99: return "F3"
        case 100: return "F8"
        case 101: return "F9"
        case 103: return "F11"
        case 109: return "F10"
        case 111: return "F12"
        case 118: return "F4"
        case 120: return "F2"
        case 122: return "F1"
        case 123: return "←"
        case 124: return "→"
        case 125: return "↓"
        case 126: return "↑"
        default: return "\(keyCode)"
        }
    }
}

// MARK: - KeyboardShortcut Codable Conformance
extension KeyboardShortcut: Codable {
    private enum CodingKeys: String, CodingKey {
        case action, keyCode, modifiers, isEnabled
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        action = try container.decode(ShortcutAction.self, forKey: .action)
        keyCode = try container.decode(UInt16.self, forKey: .keyCode)
        let modifierRawValue = try container.decode(UInt.self, forKey: .modifiers)
        modifiers = NSEvent.ModifierFlags(rawValue: modifierRawValue)
        isEnabled = try container.decode(Bool.self, forKey: .isEnabled)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(action, forKey: .action)
        try container.encode(keyCode, forKey: .keyCode)
        try container.encode(modifiers.rawValue, forKey: .modifiers)
        try container.encode(isEnabled, forKey: .isEnabled)
    }
}

enum ShortcutAction: String, CaseIterable, Codable {
    case toggleMainWindow = "toggle_main_window"
    case showDashboard = "show_dashboard"
    case showServices = "show_services"
    case showDocker = "show_docker"
    case showSystemMetrics = "show_system_metrics"
    case showLogs = "show_logs"
    case showNotifications = "show_notifications"
    case refreshData = "refresh_data"
    case toggleMenuBar = "toggle_menu_bar"
    case openSettings = "open_settings"
    case startAllServices = "start_all_services"
    case stopAllServices = "stop_all_services"
    case restartAllServices = "restart_all_services"
    case takeScreenshot = "take_screenshot"
    case exportLogs = "export_logs"
    case clearNotifications = "clear_notifications"
    case quickSearch = "quick_search"
    case focusSearchBar = "focus_search_bar"
    
    var displayName: String {
        switch self {
        case .toggleMainWindow: return "Toggle Main Window"
        case .showDashboard: return "Show Dashboard"
        case .showServices: return "Show Services"
        case .showDocker: return "Show Docker"
        case .showSystemMetrics: return "Show System Metrics"
        case .showLogs: return "Show Logs"
        case .showNotifications: return "Show Notifications"
        case .refreshData: return "Refresh Data"
        case .toggleMenuBar: return "Toggle Menu Bar"
        case .openSettings: return "Open Settings"
        case .startAllServices: return "Start All Services"
        case .stopAllServices: return "Stop All Services"
        case .restartAllServices: return "Restart All Services"
        case .takeScreenshot: return "Take Screenshot"
        case .exportLogs: return "Export Logs"
        case .clearNotifications: return "Clear Notifications"
        case .quickSearch: return "Quick Search"
        case .focusSearchBar: return "Focus Search Bar"
        }
    }
    
    var description: String {
        switch self {
        case .toggleMainWindow: return "Show or hide the main application window"
        case .showDashboard: return "Navigate to the dashboard view"
        case .showServices: return "Navigate to the services view"
        case .showDocker: return "Navigate to the Docker containers view"
        case .showSystemMetrics: return "Navigate to the system metrics view"
        case .showLogs: return "Navigate to the logs view"
        case .showNotifications: return "Navigate to the notifications view"
        case .refreshData: return "Refresh all data from the server"
        case .toggleMenuBar: return "Show or hide the menu bar extra"
        case .openSettings: return "Open the settings window"
        case .startAllServices: return "Start all stopped services"
        case .stopAllServices: return "Stop all running services"
        case .restartAllServices: return "Restart all services"
        case .takeScreenshot: return "Take a screenshot of the current view"
        case .exportLogs: return "Export logs to a file"
        case .clearNotifications: return "Clear all notifications"
        case .quickSearch: return "Open quick search overlay"
        case .focusSearchBar: return "Focus the search bar in current view"
        }
    }
    
    var category: ShortcutCategory {
        switch self {
        case .toggleMainWindow, .toggleMenuBar, .openSettings:
            return .window
        case .showDashboard, .showServices, .showDocker, .showSystemMetrics, .showLogs, .showNotifications:
            return .navigation
        case .refreshData, .takeScreenshot, .exportLogs, .clearNotifications:
            return .actions
        case .startAllServices, .stopAllServices, .restartAllServices:
            return .services
        case .quickSearch, .focusSearchBar:
            return .search
        }
    }
}

enum ShortcutCategory: String, CaseIterable {
    case window = "Window"
    case navigation = "Navigation"
    case actions = "Actions"
    case services = "Services"
    case search = "Search"
}

// MARK: - Keyboard Shortcuts Manager

@MainActor
class KeyboardShortcutsManager: ObservableObject {
    static let shared = KeyboardShortcutsManager()
    
    @Published var shortcuts: [KeyboardShortcut] = []
    @Published var isEnabled = true
    @Published var isRecording = false
    @Published var recordingAction: ShortcutAction?
    
    private var hotKeyRefs: [ShortcutAction: EventHotKeyRef] = [:]
    private var eventHandler: EventHandlerRef?
    private let userDefaults = UserDefaults.standard
    
    private init() {
        loadShortcuts()
        setupEventHandler()
        registerDefaultShortcuts()
    }
    
    deinit {
        // Clean up synchronously since we can't use async in deinit
        for (_, hotKeyRef) in hotKeyRefs {
            UnregisterEventHotKey(hotKeyRef)
        }
        if let eventHandler = eventHandler {
            RemoveEventHandler(eventHandler)
        }
    }
    
    // MARK: - Default Shortcuts
    
    private func registerDefaultShortcuts() {
        let defaultShortcuts: [(ShortcutAction, UInt16, NSEvent.ModifierFlags)] = [
            (.toggleMainWindow, 49, [.command, .shift]), // Cmd+Shift+Space
            (.showDashboard, 2, [.command, .option]), // Cmd+Opt+D
            (.showServices, 1, [.command, .option]), // Cmd+Opt+S
            (.showDocker, 2, [.command, .option, .shift]), // Cmd+Opt+Shift+D
            (.refreshData, 15, [.command, .shift]), // Cmd+Shift+R
            (.openSettings, 43, [.command]), // Cmd+,
            (.quickSearch, 3, [.command, .shift]), // Cmd+Shift+F
        ]
        
        for (action, keyCode, modifiers) in defaultShortcuts {
            if !shortcuts.contains(where: { $0.action == action }) {
                let shortcut = KeyboardShortcut(
                    action: action,
                    keyCode: keyCode,
                    modifiers: modifiers,
                    isEnabled: true
                )
                shortcuts.append(shortcut)
            }
        }
        
        saveShortcuts()
        registerHotKeys()
    }
    
    // MARK: - Shortcut Management
    
    func addShortcut(_ shortcut: KeyboardShortcut) {
        // Remove existing shortcut for the same action
        shortcuts.removeAll { $0.action == shortcut.action }
        shortcuts.append(shortcut)
        saveShortcuts()
        registerHotKeys()
    }
    
    func removeShortcut(for action: ShortcutAction) {
        shortcuts.removeAll { $0.action == action }
        saveShortcuts()
        unregisterHotKey(for: action)
    }
    
    func updateShortcut(_ shortcut: KeyboardShortcut) {
        if let index = shortcuts.firstIndex(where: { $0.action == shortcut.action }) {
            shortcuts[index] = shortcut
            saveShortcuts()
            registerHotKeys()
        }
    }
    
    func toggleShortcut(for action: ShortcutAction) {
        if let index = shortcuts.firstIndex(where: { $0.action == action }) {
            let currentShortcut = shortcuts[index]
            let updatedShortcut = KeyboardShortcut(
                action: currentShortcut.action,
                keyCode: currentShortcut.keyCode,
                modifiers: currentShortcut.modifiers,
                isEnabled: !currentShortcut.isEnabled
            )
            shortcuts[index] = updatedShortcut
            saveShortcuts()
            
            if updatedShortcut.isEnabled {
                registerHotKey(updatedShortcut)
            } else {
                unregisterHotKey(for: action)
            }
        }
    }
    
    func resetToDefaults() {
        unregisterAllHotKeys()
        shortcuts.removeAll()
        registerDefaultShortcuts()
    }
    
    // MARK: - Recording
    
    func startRecording(for action: ShortcutAction) {
        isRecording = true
        recordingAction = action
    }
    
    func stopRecording() {
        isRecording = false
        recordingAction = nil
    }
    
    func recordKeyPress(keyCode: UInt16, modifiers: NSEvent.ModifierFlags) {
        guard let action = recordingAction else { return }
        
        let shortcut = KeyboardShortcut(
            action: action,
            keyCode: keyCode,
            modifiers: modifiers,
            isEnabled: true
        )
        
        addShortcut(shortcut)
        stopRecording()
    }
    
    // MARK: - Hot Key Registration
    
    private func setupEventHandler() {
        var eventSpec = EventTypeSpec(eventClass: OSType(kEventClassKeyboard), eventKind: OSType(kEventHotKeyPressed))
        
        InstallEventHandler(
            GetApplicationEventTarget(),
            { (nextHandler, theEvent, userData) -> OSStatus in
                guard let userData = userData else { return OSStatus(eventNotHandledErr) }
                let manager = Unmanaged<KeyboardShortcutsManager>.fromOpaque(userData).takeUnretainedValue()
                return manager.handleHotKeyEvent(theEvent)
            },
            1,
            &eventSpec,
            Unmanaged.passUnretained(self).toOpaque(),
            &eventHandler
        )
    }
    
    private func handleHotKeyEvent(_ event: EventRef?) -> OSStatus {
        guard let event = event else { return OSStatus(eventNotHandledErr) }
        
        var hotKeyID = EventHotKeyID()
        let status = GetEventParameter(
            event,
            EventParamName(kEventParamDirectObject),
            EventParamType(typeEventHotKeyID),
            nil,
            MemoryLayout<EventHotKeyID>.size,
            nil,
            &hotKeyID
        )
        
        guard status == noErr else { return OSStatus(eventNotHandledErr) }
        
        let actionRawValue = hotKeyID.id
        guard let action = ShortcutAction(rawValue: String(actionRawValue)) else {
            return OSStatus(eventNotHandledErr)
        }
        
        executeAction(action)
        return noErr
    }
    
    private func registerHotKeys() {
        unregisterAllHotKeys()
        
        for shortcut in shortcuts where shortcut.isEnabled {
            registerHotKey(shortcut)
        }
    }
    
    private func registerHotKey(_ shortcut: KeyboardShortcut) {
        var hotKeyRef: EventHotKeyRef?
        let hotKeyID = EventHotKeyID(
            signature: OSType(fourCharCodeFrom: "WOWM"),
            id: UInt32(shortcut.action.rawValue.hashValue)
        )
        
        let status = RegisterEventHotKey(
            shortcut.keyCode,
            UInt32(shortcut.modifiers.rawValue),
            hotKeyID,
            GetApplicationEventTarget(),
            0,
            &hotKeyRef
        )
        
        if status == noErr, let hotKeyRef = hotKeyRef {
            hotKeyRefs[shortcut.action] = hotKeyRef
        } else {
            print("Failed to register hot key for \(shortcut.action): \(status)")
        }
    }
    
    private func unregisterHotKey(for action: ShortcutAction) {
        if let hotKeyRef = hotKeyRefs[action] {
            UnregisterEventHotKey(hotKeyRef)
            hotKeyRefs.removeValue(forKey: action)
        }
    }
    
    private func unregisterAllHotKeys() {
        for (_, hotKeyRef) in hotKeyRefs {
            UnregisterEventHotKey(hotKeyRef)
        }
        hotKeyRefs.removeAll()
    }
    
    private func fourCharCodeFrom(_ string: String) -> FourCharCode {
        let utf8 = string.utf8
        var result: FourCharCode = 0
        for (i, byte) in utf8.enumerated() {
            if i >= 4 { break }
            result = result << 8 + FourCharCode(byte)
        }
        return result
    }
    
    // MARK: - Action Execution
    
    private func executeAction(_ action: ShortcutAction) {
        guard isEnabled else { return }
        
        Task { @MainActor in
            switch action {
            case .toggleMainWindow:
                toggleMainWindow()
            case .showDashboard:
                navigateToView(.dashboard)
            case .showServices:
                navigateToView(.services)
            case .showDocker:
                navigateToView(.docker)
            case .showSystemMetrics:
                navigateToView(.systemMetrics)
            case .showLogs:
                navigateToView(.logs)
            case .showNotifications:
                navigateToView(.notifications)
            case .refreshData:
                await AppState.shared.refreshAllData()
            case .toggleMenuBar:
                toggleMenuBar()
            case .openSettings:
                openSettings()
            case .startAllServices:
                await AppState.shared.startAllServices()
            case .stopAllServices:
                await AppState.shared.stopAllServices()
            case .restartAllServices:
                await AppState.shared.restartAllServices()
            case .takeScreenshot:
                takeScreenshot()
            case .exportLogs:
                exportLogs()
            case .clearNotifications:
                AppState.shared.clearAllNotifications()
            case .quickSearch:
                showQuickSearch()
            case .focusSearchBar:
                focusSearchBar()
            }
        }
    }
    
    private func toggleMainWindow() {
        if let window = NSApplication.shared.windows.first(where: { $0.title.contains("WOW") }) {
            if window.isVisible {
                window.orderOut(nil)
            } else {
                window.makeKeyAndOrderFront(nil)
                NSApplication.shared.activate(ignoringOtherApps: true)
            }
        }
    }
    
    private func navigateToView(_ view: NavigationDestination) {
        // Post notification to navigate to specific view
        NotificationCenter.default.post(
            name: .navigateToView,
            object: view
        )
        
        // Ensure main window is visible
        if let window = NSApplication.shared.windows.first(where: { $0.title.contains("WOW") }) {
            window.makeKeyAndOrderFront(nil)
            NSApplication.shared.activate(ignoringOtherApps: true)
        }
    }
    
    private func toggleMenuBar() {
        // Implementation for toggling menu bar extra
        NotificationCenter.default.post(name: .toggleMenuBar, object: nil)
    }
    
    private func openSettings() {
        NotificationCenter.default.post(name: .openSettings, object: nil)
    }
    
    private func takeScreenshot() {
        // Implementation for taking screenshot
        NotificationCenter.default.post(name: .takeScreenshot, object: nil)
    }
    
    private func exportLogs() {
        NotificationCenter.default.post(name: .exportLogs, object: nil)
    }
    
    private func showQuickSearch() {
        NotificationCenter.default.post(name: .showQuickSearch, object: nil)
    }
    
    private func focusSearchBar() {
        NotificationCenter.default.post(name: .focusSearchBar, object: nil)
    }
    
    // MARK: - Persistence
    
    private func saveShortcuts() {
        if let data = try? JSONEncoder().encode(shortcuts) {
            userDefaults.set(data, forKey: "KeyboardShortcuts")
        }
    }
    
    private func loadShortcuts() {
        guard let data = userDefaults.data(forKey: "KeyboardShortcuts"),
              let loadedShortcuts = try? JSONDecoder().decode([KeyboardShortcut].self, from: data) else {
            return
        }
        shortcuts = loadedShortcuts
    }
}

// MARK: - Supporting Types

enum NavigationDestination {
    case dashboard
    case services
    case docker
    case systemMetrics
    case logs
    case notifications
}

// MARK: - Notification Names

extension Notification.Name {
    static let navigateToView = Notification.Name("navigateToView")
    static let toggleMenuBar = Notification.Name("toggleMenuBar")
    static let openSettings = Notification.Name("openSettings")
    static let takeScreenshot = Notification.Name("takeScreenshot")
    static let exportLogs = Notification.Name("exportLogs")
    // showQuickSearch is defined in App.swift
    static let focusSearchBar = Notification.Name("focusSearchBar")
}

// MARK: - AppState Extensions

// AppState extensions are defined in other files

// MARK: - Key Recording View

struct KeyRecordingView: View {
    @StateObject private var shortcutsManager = KeyboardShortcutsManager.shared
    let action: ShortcutAction
    let onComplete: (KeyboardShortcut?) -> Void
    
    @State private var recordedKeyCode: UInt16?
    @State private var recordedModifiers: NSEvent.ModifierFlags = []
    @State private var isListening = false
    
    var body: some View {
        VStack(spacing: 16) {
            Text("Record Shortcut")
                .font(.headline)
            
            Text("Press the key combination for \(action.displayName)")
                .multilineTextAlignment(.center)
            
            if isListening {
                VStack(spacing: 8) {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("Listening for key combination...")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color(NSColor.controlBackgroundColor))
                .cornerRadius(8)
            } else if let keyCode = recordedKeyCode {
                VStack(spacing: 8) {
                    Text("Recorded:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(modifiersToString(recordedModifiers) + keyCodeToString(keyCode))
                        .font(.title2)
                        .fontWeight(.medium)
                        .padding()
                        .background(Color.accentColor.opacity(0.1))
                        .cornerRadius(8)
                }
            }
            
            HStack(spacing: 12) {
                Button("Cancel") {
                    onComplete(nil)
                }
                .buttonStyle(.bordered)
                
                if recordedKeyCode != nil {
                    Button("Save") {
                        if let keyCode = recordedKeyCode {
                            let shortcut = KeyboardShortcut(
                                action: action,
                                keyCode: keyCode,
                                modifiers: recordedModifiers,
                                isEnabled: true
                            )
                            onComplete(shortcut)
                        }
                    }
                    .buttonStyle(.borderedProminent)
                }
                
                if !isListening {
                    Button(recordedKeyCode == nil ? "Start Recording" : "Record Again") {
                        startListening()
                    }
                    .buttonStyle(.bordered)
                }
            }
        }
        .padding()
        .frame(width: 300)
        .onAppear {
            startListening()
        }
    }
    
    private func startListening() {
        isListening = true
        recordedKeyCode = nil
        recordedModifiers = []
        
        // Set up local event monitor
        NSEvent.addLocalMonitorForEvents(matching: [.keyDown]) { event in
            if isListening {
                recordedKeyCode = event.keyCode
                recordedModifiers = event.modifierFlags.intersection([.command, .option, .control, .shift])
                isListening = false
                return nil // Consume the event
            }
            return event
        }
    }
    
    private func modifiersToString(_ modifiers: NSEvent.ModifierFlags) -> String {
        var components: [String] = []
        
        if modifiers.contains(.command) {
            components.append("⌘")
        }
        if modifiers.contains(.option) {
            components.append("⌥")
        }
        if modifiers.contains(.control) {
            components.append("⌃")
        }
        if modifiers.contains(.shift) {
            components.append("⇧")
        }
        
        return components.joined()
    }
    
    private func keyCodeToString(_ keyCode: UInt16) -> String {
        // Reuse the keyCodeToString method from KeyboardShortcut
        let shortcut = KeyboardShortcut(action: action, keyCode: keyCode, modifiers: [], isEnabled: true)
        return String(shortcut.displayString.dropFirst(modifiersToString(recordedModifiers).count))
    }
}

#Preview {
    KeyRecordingView(action: .toggleMainWindow) { _ in }
}