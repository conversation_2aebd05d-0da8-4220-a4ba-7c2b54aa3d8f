import Foundation
import UserNotifications
import SwiftUI

@MainActor
class NotificationManager: NSObject, ObservableObject {
    static let shared = NotificationManager()
    
    @Published var isAuthorized = false
    @Published var notificationSettings: UNNotificationSettings?
    
    private let center = UNUserNotificationCenter.current()
    
    override init() {
        super.init()
        center.delegate = self
        checkAuthorizationStatus()
    }
    
    // MARK: - Authorization
    
    func requestAuthorization() async {
        do {
            let granted = try await center.requestAuthorization(options: [
                .alert,
                .sound,
                .badge,
                .provisional
            ])
            
            isAuthorized = granted
            
            if granted {
                await getNotificationSettings()
                registerNotificationCategories()
            }
        } catch {
            print("Failed to request notification authorization: \(error)")
        }
    }
    
    func requestPermission() async {
        await requestAuthorization()
    }
    
    func checkAuthorizationStatus() {
        Task {
            let settings = await center.notificationSettings()
            notificationSettings = settings
            isAuthorized = settings.authorizationStatus == .authorized || settings.authorizationStatus == .provisional
        }
    }
    
    private func getNotificationSettings() async {
        notificationSettings = await center.notificationSettings()
    }
    
    // MARK: - Notification Categories
    
    private func registerNotificationCategories() {
        let categories: Set<UNNotificationCategory> = [
            createServiceCategory(),
            createDockerCategory(),
            createSystemCategory(),
            createErrorCategory()
        ]
        
        center.setNotificationCategories(categories)
    }
    
    private func createServiceCategory() -> UNNotificationCategory {
        let restartAction = UNNotificationAction(
            identifier: "RESTART_SERVICE",
            title: "Restart",
            options: []
        )
        
        let stopAction = UNNotificationAction(
            identifier: "STOP_SERVICE",
            title: "Stop",
            options: [.destructive]
        )
        
        let viewAction = UNNotificationAction(
            identifier: "VIEW_SERVICE",
            title: "View Details",
            options: [.foreground]
        )
        
        return UNNotificationCategory(
            identifier: "SERVICE_NOTIFICATION",
            actions: [restartAction, stopAction, viewAction],
            intentIdentifiers: [],
            options: []
        )
    }
    
    private func createDockerCategory() -> UNNotificationCategory {
        let restartAction = UNNotificationAction(
            identifier: "RESTART_CONTAINER",
            title: "Restart",
            options: []
        )
        
        let stopAction = UNNotificationAction(
            identifier: "STOP_CONTAINER",
            title: "Stop",
            options: [.destructive]
        )
        
        let viewAction = UNNotificationAction(
            identifier: "VIEW_CONTAINER",
            title: "View Details",
            options: [.foreground]
        )
        
        return UNNotificationCategory(
            identifier: "DOCKER_NOTIFICATION",
            actions: [restartAction, stopAction, viewAction],
            intentIdentifiers: [],
            options: []
        )
    }
    
    private func createSystemCategory() -> UNNotificationCategory {
        let viewAction = UNNotificationAction(
            identifier: "VIEW_METRICS",
            title: "View Metrics",
            options: [.foreground]
        )
        
        let dismissAction = UNNotificationAction(
            identifier: "DISMISS_ALERT",
            title: "Dismiss",
            options: []
        )
        
        return UNNotificationCategory(
            identifier: "SYSTEM_NOTIFICATION",
            actions: [viewAction, dismissAction],
            intentIdentifiers: [],
            options: []
        )
    }
    
    private func createErrorCategory() -> UNNotificationCategory {
        let retryAction = UNNotificationAction(
            identifier: "RETRY_ACTION",
            title: "Retry",
            options: []
        )
        
        let viewAction = UNNotificationAction(
            identifier: "VIEW_LOGS",
            title: "View Logs",
            options: [.foreground]
        )
        
        return UNNotificationCategory(
            identifier: "ERROR_NOTIFICATION",
            actions: [retryAction, viewAction],
            intentIdentifiers: [],
            options: []
        )
    }
    
    // MARK: - Send Notifications
    
    func sendServiceNotification(
        serviceName: String,
        status: ServiceStatus,
        message: String? = nil
    ) {
        guard isAuthorized else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "Service Update"
        content.body = message ?? "\(serviceName) is now \(status.rawValue)"
        content.categoryIdentifier = "SERVICE_NOTIFICATION"
        content.userInfo = [
            "type": "service",
            "serviceName": serviceName,
            "status": status.rawValue
        ]
        
        // Set sound based on status
        switch status {
        case .error:
            content.sound = .defaultCritical
        case .stopped:
            content.sound = .default
        default:
            content.sound = nil
        }
        
        let request = UNNotificationRequest(
            identifier: "service-\(serviceName)-\(UUID().uuidString)",
            content: content,
            trigger: nil
        )
        
        center.add(request) { error in
            if let error = error {
                print("Failed to send service notification: \(error)")
            }
        }
    }
    
    func sendDockerNotification(
        containerName: String,
        state: String,
        message: String? = nil
    ) {
        guard isAuthorized else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "Docker Update"
        content.body = message ?? "Container \(containerName) is now \(state)"
        content.categoryIdentifier = "DOCKER_NOTIFICATION"
        content.userInfo = [
            "type": "docker",
            "containerName": containerName,
            "state": state
        ]
        
        if state == "exited" {
            content.sound = .default
        }
        
        let request = UNNotificationRequest(
            identifier: "docker-\(containerName)-\(UUID().uuidString)",
            content: content,
            trigger: nil
        )
        
        center.add(request) { error in
            if let error = error {
                print("Failed to send docker notification: \(error)")
            }
        }
    }
    
    func sendSystemAlert(
        title: String,
        message: String,
        priority: NotificationPriority = .normal,
        category: NotificationCategory = .system
    ) {
        guard isAuthorized else { return }
        
        let content = UNMutableNotificationContent()
        content.title = title
        content.body = message
        content.categoryIdentifier = "SYSTEM_NOTIFICATION"
        content.userInfo = [
            "type": "system",
            "priority": priority.rawValue,
            "category": category.rawValue
        ]
        
        // Set sound and interruption level based on priority
        switch priority {
        case .critical:
            content.sound = .defaultCritical
            content.interruptionLevel = .critical
        case .high:
            content.sound = .default
            content.interruptionLevel = .active
        case .normal:
            content.sound = nil
            content.interruptionLevel = .active
        case .low:
            content.sound = nil
            content.interruptionLevel = .passive
        }
        
        let request = UNNotificationRequest(
            identifier: "system-\(UUID().uuidString)",
            content: content,
            trigger: nil
        )
        
        center.add(request) { error in
            if let error = error {
                print("Failed to send system notification: \(error)")
            }
        }
    }
    
    func sendErrorNotification(
        title: String,
        error: Error,
        context: String? = nil
    ) {
        guard isAuthorized else { return }
        
        let content = UNMutableNotificationContent()
        content.title = title
        content.body = context != nil ? "\(context!): \(error.localizedDescription)" : error.localizedDescription
        content.categoryIdentifier = "ERROR_NOTIFICATION"
        content.sound = .defaultCritical
        content.interruptionLevel = .critical
        content.userInfo = [
            "type": "error",
            "context": context ?? "",
            "error": error.localizedDescription
        ]
        
        let request = UNNotificationRequest(
            identifier: "error-\(UUID().uuidString)",
            content: content,
            trigger: nil
        )
        
        center.add(request) { error in
            if let error = error {
                print("Failed to send error notification: \(error)")
            }
        }
    }
    
    // MARK: - Scheduled Notifications
    
    func scheduleMaintenanceReminder(
        title: String,
        message: String,
        date: Date
    ) {
        guard isAuthorized else { return }
        
        let content = UNMutableNotificationContent()
        content.title = title
        content.body = message
        content.categoryIdentifier = "SYSTEM_NOTIFICATION"
        content.sound = .default
        
        let calendar = Calendar.current
        let components = calendar.dateComponents([.year, .month, .day, .hour, .minute], from: date)
        let trigger = UNCalendarNotificationTrigger(dateMatching: components, repeats: false)
        
        let request = UNNotificationRequest(
            identifier: "maintenance-\(UUID().uuidString)",
            content: content,
            trigger: trigger
        )
        
        center.add(request) { error in
            if let error = error {
                print("Failed to schedule maintenance reminder: \(error)")
            }
        }
    }
    
    // MARK: - Notification Management
    
    func getPendingNotifications() async -> [UNNotificationRequest] {
        return await center.pendingNotificationRequests()
    }
    
    func getDeliveredNotifications() async -> [UNNotification] {
        return await center.deliveredNotifications()
    }
    
    func removeNotification(withIdentifier identifier: String) {
        center.removePendingNotificationRequests(withIdentifiers: [identifier])
        center.removeDeliveredNotifications(withIdentifiers: [identifier])
    }
    
    func removeAllNotifications() {
        center.removeAllPendingNotificationRequests()
        center.removeAllDeliveredNotifications()
    }
    
    func setBadgeCount(_ count: Int) {
        Task {
            try? await center.setBadgeCount(count)
        }
    }
    
    // MARK: - Notification Testing
    
    func sendTestNotification() {
        sendSystemAlert(
            title: "Test Notification",
            message: "This is a test notification from WOW Monitor.",
            priority: .normal
        )
    }
}

// MARK: - UNUserNotificationCenterDelegate

extension NotificationManager: UNUserNotificationCenterDelegate {
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        // Show notification even when app is in foreground
        completionHandler([.banner, .sound, .badge])
    }
    
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        handleNotificationResponse(response)
        completionHandler()
    }
    
    private func handleNotificationResponse(_ response: UNNotificationResponse) {
        let userInfo = response.notification.request.content.userInfo
        let actionIdentifier = response.actionIdentifier
        
        Task {
            await MainActor.run {
                switch actionIdentifier {
                case "RESTART_SERVICE":
                    if let serviceName = userInfo["serviceName"] as? String {
                        handleRestartService(serviceName)
                    }
                    
                case "STOP_SERVICE":
                    if let serviceName = userInfo["serviceName"] as? String {
                        handleStopService(serviceName)
                    }
                    
                case "VIEW_SERVICE":
                    if let serviceName = userInfo["serviceName"] as? String {
                        handleViewService(serviceName)
                    }
                    
                case "RESTART_CONTAINER":
                    if let containerName = userInfo["containerName"] as? String {
                        handleRestartContainer(containerName)
                    }
                    
                case "STOP_CONTAINER":
                    if let containerName = userInfo["containerName"] as? String {
                        handleStopContainer(containerName)
                    }
                    
                case "VIEW_CONTAINER":
                    if let containerName = userInfo["containerName"] as? String {
                        handleViewContainer(containerName)
                    }
                    
                case "VIEW_METRICS":
                    handleViewMetrics()
                    
                case "VIEW_LOGS":
                    handleViewLogs()
                    
                case "RETRY_ACTION":
                    handleRetryAction(userInfo)
                    
                case UNNotificationDefaultActionIdentifier:
                    // User tapped the notification
                    handleDefaultAction(userInfo)
                    
                default:
                    break
                }
            }
        }
    }
    
    // MARK: - Action Handlers
    
    private func handleRestartService(_ serviceName: String) {
        Task {
            await AppState.shared.restartService(serviceName)
        }
    }
    
    private func handleStopService(_ serviceName: String) {
        Task {
            await AppState.shared.stopService(serviceName)
        }
    }
    
    private func handleViewService(_ serviceName: String) {
        // Open main window and navigate to service
        NSApp.activate(ignoringOtherApps: true)
        // Implementation would depend on your navigation system
    }
    
    private func handleRestartContainer(_ containerName: String) {
        Task {
            // Implementation for restarting container
        }
    }
    
    private func handleStopContainer(_ containerName: String) {
        Task {
            // Implementation for stopping container
        }
    }
    
    private func handleViewContainer(_ containerName: String) {
        // Open main window and navigate to container
        NSApp.activate(ignoringOtherApps: true)
    }
    
    private func handleViewMetrics() {
        // Open main window and navigate to metrics
        NSApp.activate(ignoringOtherApps: true)
    }
    
    private func handleViewLogs() {
        // Open main window and navigate to logs
        NSApp.activate(ignoringOtherApps: true)
    }
    
    private func handleRetryAction(_ userInfo: [AnyHashable: Any]) {
        // Implementation for retry action based on context
    }
    
    private func handleDefaultAction(_ userInfo: [AnyHashable: Any]) {
        // Handle default tap action
        NSApp.activate(ignoringOtherApps: true)
    }
}

// MARK: - Notification Preferences

struct NotificationPreferences {
    var enableServiceNotifications = true
    var enableDockerNotifications = true
    var enableSystemNotifications = true
    var enableErrorNotifications = true
    var enableSounds = true
    var enableBadges = true
    var criticalAlertsOnly = false
    
    // Service-specific preferences
    var notifyOnServiceStart = false
    var notifyOnServiceStop = true
    var notifyOnServiceError = true
    var notifyOnServiceRestart = false
    
    // Docker-specific preferences
    var notifyOnContainerStart = false
    var notifyOnContainerStop = true
    var notifyOnContainerError = true
    var notifyOnImagePull = false
    
    // System-specific preferences
    var notifyOnHighCPU = true
    var notifyOnHighMemory = true
    var notifyOnLowDisk = true
    var notifyOnConnectionLoss = true
    
    // Thresholds
    var cpuThreshold: Double = 80.0
    var memoryThreshold: Double = 85.0
    var diskThreshold: Double = 90.0
}

// MARK: - AppState Extension

extension AppState {
    static var shared: AppState {
        // This would be your singleton instance
        return AppState()
    }
}