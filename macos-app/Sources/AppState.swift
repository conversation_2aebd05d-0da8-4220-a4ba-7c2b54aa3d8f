import SwiftUI
import Combine
import Logging
import SocketIO

class AppState: ObservableObject {
    @Published var isConnected = false
    @Published var serverStatus: ServerStatus = .unknown
    @Published var services: [ServiceInfo] = []
    @Published var notifications: [NotificationItem] = []
    @Published var dockerContainers: [DockerContainer] = []
    @Published var systemMetrics: SystemMetrics?
    @Published var logs: [LogEntry] = []
    @Published var serverURL: String = "http://localhost:3000"
    
    private let logger = Logger(label: "WOWMacOS.AppState")
    private let apiClient: APIClient
    private let socketManager: SocketManager
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        self.apiClient = APIClient()
        self.socketManager = SocketManager()
        
        setupSocketConnection()
        startPeriodicUpdates()
    }
    
    private func setupSocketConnection() {
        socketManager.onConnect = { [weak self] in
            DispatchQueue.main.async {
                self?.isConnected = true
                self?.logger.info("Connected to WOW backend")
            }
        }
        
        socketManager.onDisconnect = { [weak self] in
            DispatchQueue.main.async {
                self?.isConnected = false
                self?.logger.warning("Disconnected from WOW backend")
            }
        }
        
        socketManager.onServiceUpdate = { [weak self] services in
            DispatchQueue.main.async {
                self?.services = services
            }
        }
        
        socketManager.onDockerUpdate = { [weak self] containers in
            DispatchQueue.main.async {
                self?.dockerContainers = containers
            }
        }
        
        socketManager.onMetricsUpdate = { [weak self] metrics in
            DispatchQueue.main.async {
                self?.systemMetrics = metrics
            }
        }
    }
    
    private func startPeriodicUpdates() {
        Timer.publish(every: 30, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                self?.refreshData()
            }
            .store(in: &cancellables)
    }
    
    func connect() {
        socketManager.connect()
        refreshData()
    }
    
    func disconnect() {
        socketManager.disconnect()
    }
    
    @MainActor
    func initialize() async {
        logger.info("Initializing AppState")
        socketManager.connect()
        await refreshData()
    }
    
    func refreshData() {
        Task {
            await fetchServerStatus()
            await fetchServices()
            await fetchDockerContainers()
            await fetchSystemMetrics()
        }
    }
    
    func refreshSystemMetrics() {
        Task {
            await fetchSystemMetrics()
        }
    }
    
    @MainActor
    private func fetchServerStatus() async {
        do {
            let status = try await apiClient.getServerStatus()
            self.serverStatus = status
        } catch {
            logger.error("Failed to fetch server status: \(error)")
            self.serverStatus = .error
        }
    }
    
    @MainActor
    private func fetchServices() async {
        do {
            let services = try await apiClient.getServices()
            self.services = services
        } catch {
            logger.error("Failed to fetch services: \(error)")
        }
    }
    
    @MainActor
    private func fetchDockerContainers() async {
        do {
            let containers = try await apiClient.getDockerContainers()
            self.dockerContainers = containers
        } catch {
            logger.error("Failed to fetch Docker containers: \(error)")
        }
    }
    
    @MainActor
    private func fetchSystemMetrics() async {
        do {
            let metrics = try await apiClient.getSystemMetrics()
            self.systemMetrics = metrics
        } catch {
            logger.error("Failed to fetch system metrics: \(error)")
        }
    }
    
    func restartService(_ serviceId: String) async {
        do {
            try await apiClient.restartService(serviceId)
            await fetchServices()
            addNotification("Service \(serviceId) restarted successfully", type: .success)
        } catch {
            logger.error("Failed to restart service \(serviceId): \(error)")
            addNotification("Failed to restart service \(serviceId)", type: .error)
        }
    }
    
    func startService(_ serviceId: String) async {
        do {
            try await apiClient.startService(serviceId)
            await fetchServices()
            addNotification("Service \(serviceId) started successfully", type: .success)
        } catch {
            logger.error("Failed to start service \(serviceId): \(error)")
            addNotification("Failed to start service \(serviceId)", type: .error)
        }
    }
    
    func stopService(_ serviceId: String) async {
        do {
            try await apiClient.stopService(serviceId)
            await fetchServices()
            addNotification("Service \(serviceId) stopped successfully", type: .success)
        } catch {
            logger.error("Failed to stop service \(serviceId): \(error)")
            addNotification("Failed to stop service \(serviceId)", type: .error)
        }
    }
    
    func restartDockerContainer(_ containerId: String) async {
        do {
            try await apiClient.restartDockerContainer(containerId)
            await fetchDockerContainers()
            addNotification("Container restarted successfully", type: .success)
        } catch {
            logger.error("Failed to restart container \(containerId): \(error)")
            addNotification("Failed to restart container", type: .error)
        }
    }
    
    private func addNotification(_ message: String, type: NotificationType) {
        let notification = NotificationItem(
            id: UUID(),
            message: message,
            type: type,
            timestamp: Date()
        )
        notifications.insert(notification, at: 0)
        
        // Keep only last 50 notifications
        if notifications.count > 50 {
            notifications = Array(notifications.prefix(50))
        }
    }
}

enum ServerStatus {
    case unknown
    case healthy
    case warning
    case error
    
    var color: Color {
        switch self {
        case .unknown: return .gray
        case .healthy: return .green
        case .warning: return .orange
        case .error: return .red
        }
    }
    
    var icon: String {
        switch self {
        case .unknown: return "questionmark.circle"
        case .healthy: return "checkmark.circle.fill"
        case .warning: return "exclamationmark.triangle.fill"
        case .error: return "xmark.circle.fill"
        }
    }
}