import Foundation
import SwiftUI
import Combine

// MARK: - Preferences Models

struct AppPreferences: Codable, Equatable {
    var general: GeneralPreferences = GeneralPreferences()
    var connection: ConnectionPreferences = ConnectionPreferences()
    var notifications: NotificationPreferences = NotificationPreferences()
    var appearance: AppearancePreferences = AppearancePreferences()
    var advanced: AdvancedPreferences = AdvancedPreferences()
    var shortcuts: ShortcutPreferences = ShortcutPreferences()
    
    static let `default` = AppPreferences()
}

struct GeneralPreferences: Codable {
    var launchAtLogin: Bool = false
    var startMinimized: Bool = false
    var showMenuBarIcon: Bool = true
    var autoRefreshInterval: TimeInterval = 30
    var enableAutoRefresh: Bool = true
    var confirmBeforeQuitting: Bool = true
    var language: String = "en"
    var dateFormat: String = "yyyy-MM-dd HH:mm:ss"
    var timeZone: String = TimeZone.current.identifier
}

struct ConnectionPreferences: Codable {
    var serverURL: String = "http://localhost:3000"
    var apiKey: String = ""
    var connectionTimeout: TimeInterval = 30
    var retryAttempts: Int = 3
    var retryDelay: TimeInterval = 5
    var enableSSLVerification: Bool = true
    var customHeaders: [String: String] = [:]
    var proxyEnabled: Bool = false
    var proxyHost: String = ""
    var proxyPort: Int = 8080
    var proxyUsername: String = ""
    var proxyPassword: String = ""
}

struct NotificationPreferences: Codable {
    var enabled: Bool = true
    var soundEnabled: Bool = true
    var badgeCount: Bool = true
    var showInMenuBar: Bool = true
    var serviceNotifications: Bool = true
    var dockerNotifications: Bool = true
    var systemNotifications: Bool = true
    var errorNotifications: Bool = true
    var criticalOnly: Bool = false
    var quietHours: QuietHours = QuietHours()
    var customSounds: [String: String] = [:]
    
    struct QuietHours: Codable {
        var enabled: Bool = false
        var startTime: String = "22:00"
        var endTime: String = "08:00"
        var weekendsOnly: Bool = false
    }
}

struct AppearancePreferences: Codable {
    var theme: AppTheme = .system
    var accentColor: String = "blue"
    var fontSize: FontSize = .medium
    var compactMode: Bool = false
    var showSidebar: Bool = true
    var sidebarWidth: Double = 200
    var windowOpacity: Double = 1.0
    var reduceMotion: Bool = false
    var highContrast: Bool = false
    
    var colorScheme: ColorScheme? {
        switch theme {
        case .light: return .light
        case .dark: return .dark
        case .system: return nil
        }
    }
    
    enum AppTheme: String, Codable, CaseIterable {
        case light = "light"
        case dark = "dark"
        case system = "system"
        
        var displayName: String {
            switch self {
            case .light: return "Light"
            case .dark: return "Dark"
            case .system: return "System"
            }
        }
    }
    
    enum FontSize: String, Codable, CaseIterable {
        case small = "small"
        case medium = "medium"
        case large = "large"
        
        var displayName: String {
            switch self {
            case .small: return "Small"
            case .medium: return "Medium"
            case .large: return "Large"
            }
        }
        
        var scaleFactor: Double {
            switch self {
            case .small: return 0.9
            case .medium: return 1.0
            case .large: return 1.1
            }
        }
    }
}

struct AdvancedPreferences: Codable {
    var enableDebugMode: Bool = false
    var logLevel: LogLevel = .info
    var maxLogEntries: Int = 1000
    var enableCrashReporting: Bool = true
    var enableAnalytics: Bool = false
    var cacheSize: Int = 100 // MB
    var enableExperimentalFeatures: Bool = false
    var customUserAgent: String = ""
    var networkTimeout: TimeInterval = 30
    var maxConcurrentRequests: Int = 10
    

}

struct ShortcutPreferences: Codable {
    var globalShortcuts: [String: String] = [
        "toggleMainWindow": "⌘⇧W",
        "quickSearch": "⌘⇧F",
        "refreshData": "⌘R",
        "showNotifications": "⌘⇧N"
    ]
    var enableGlobalShortcuts: Bool = true
    var customShortcuts: [String: String] = [:]
}

// MARK: - Preferences Manager

@MainActor
class PreferencesManager: ObservableObject {
    static let shared = PreferencesManager()
    
    @Published var preferences: AppPreferences {
        didSet {
            savePreferences()
            notifyPreferencesChanged()
        }
    }
    
    private let userDefaults = UserDefaults.standard
    private let preferencesKey = "WOWAppPreferences"
    private let encoder = JSONEncoder()
    private let decoder = JSONDecoder()
    
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        self.preferences = Self.loadPreferences()
        setupPreferencesObserver()
    }
    
    // MARK: - Loading and Saving
    
    private static func loadPreferences() -> AppPreferences {
        let userDefaults = UserDefaults.standard
        let decoder = JSONDecoder()
        
        guard let data = userDefaults.data(forKey: "WOWAppPreferences"),
              let preferences = try? decoder.decode(AppPreferences.self, from: data) else {
            return AppPreferences.default
        }
        
        return preferences
    }
    
    private func savePreferences() {
        do {
            let data = try encoder.encode(preferences)
            userDefaults.set(data, forKey: preferencesKey)
            userDefaults.synchronize()
        } catch {
            print("Failed to save preferences: \(error)")
        }
    }
    
    private func setupPreferencesObserver() {
        // Observe changes to specific preference categories
        $preferences
            .map(\.appearance)
            .removeDuplicates()
            .sink { [weak self] _ in
                self?.applyAppearanceChanges()
            }
            .store(in: &cancellables)
        
        $preferences
            .map(\.general.launchAtLogin)
            .removeDuplicates()
            .sink { [weak self] launchAtLogin in
                self?.updateLaunchAtLogin(launchAtLogin)
            }
            .store(in: &cancellables)
    }
    
    private func notifyPreferencesChanged() {
        NotificationCenter.default.post(
            name: .preferencesDidChange,
            object: self,
            userInfo: ["preferences": preferences]
        )
    }
    
    // MARK: - Preference Updates
    
    func updateGeneral(_ update: (inout GeneralPreferences) -> Void) {
        update(&preferences.general)
    }
    
    func updateConnection(_ update: (inout ConnectionPreferences) -> Void) {
        update(&preferences.connection)
    }
    
    func updateNotifications(_ update: (inout NotificationPreferences) -> Void) {
        update(&preferences.notifications)
    }
    
    func applyPreferences() {
        applyAppearanceChanges()
        applyGeneralChanges()
    }
    
    func updateAppearance(_ update: (inout AppearancePreferences) -> Void) {
        update(&preferences.appearance)
    }
    
    func updateAdvanced(_ update: (inout AdvancedPreferences) -> Void) {
        update(&preferences.advanced)
    }
    
    func updateShortcuts(_ update: (inout ShortcutPreferences) -> Void) {
        update(&preferences.shortcuts)
    }
    
    // MARK: - Specific Preference Actions
    
    private func applyAppearanceChanges() {
        // Apply theme changes
        switch preferences.appearance.theme {
        case .light:
            NSApp.appearance = NSAppearance(named: .aqua)
        case .dark:
            NSApp.appearance = NSAppearance(named: .darkAqua)
        case .system:
            NSApp.appearance = nil
        }
        
        // Apply other appearance changes
        NotificationCenter.default.post(
            name: .appearanceDidChange,
            object: self,
            userInfo: ["appearance": preferences.appearance]
        )
    }
    
    private func updateLaunchAtLogin(_ enabled: Bool) {
        // Implementation for launch at login
        // This would typically involve LaunchServices or similar APIs
        print("Launch at login: \(enabled)")
    }
    
    // MARK: - Import/Export
    
    func exportPreferences() throws -> Data {
        return try encoder.encode(preferences)
    }
    
    func importPreferences(from data: Data) throws {
        let importedPreferences = try decoder.decode(AppPreferences.self, from: data)
        preferences = importedPreferences
    }
    
    func resetToDefaults() {
        preferences = AppPreferences.default
    }
    
    func resetCategory<T>(_ keyPath: WritableKeyPath<AppPreferences, T>, to defaultValue: T) {
        preferences[keyPath: keyPath] = defaultValue
    }
    
    // MARK: - Validation
    
    func validatePreferences() -> [PreferenceValidationError] {
        var errors: [PreferenceValidationError] = []
        
        // Validate connection preferences
        if preferences.connection.serverURL.isEmpty {
            errors.append(.invalidServerURL)
        }
        
        if preferences.connection.connectionTimeout <= 0 {
            errors.append(.invalidTimeout)
        }
        
        if preferences.connection.retryAttempts < 0 {
            errors.append(.invalidRetryAttempts)
        }
        
        // Validate general preferences
        if preferences.general.autoRefreshInterval < 5 {
            errors.append(.invalidRefreshInterval)
        }
        
        // Validate advanced preferences
        if preferences.advanced.maxLogEntries < 100 {
            errors.append(.invalidLogEntries)
        }
        
        if preferences.advanced.cacheSize < 10 {
            errors.append(.invalidCacheSize)
        }
        
        return errors
    }
    
    // MARK: - Migration
    
    func migratePreferencesIfNeeded() {
        let currentVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"
        let lastMigratedVersion = userDefaults.string(forKey: "LastMigratedPreferencesVersion") ?? "0.0.0"
        
        if currentVersion != lastMigratedVersion {
            performMigration(from: lastMigratedVersion, to: currentVersion)
            userDefaults.set(currentVersion, forKey: "LastMigratedPreferencesVersion")
        }
    }
    
    private func performMigration(from oldVersion: String, to newVersion: String) {
        print("Migrating preferences from \(oldVersion) to \(newVersion)")
        
        // Perform version-specific migrations
        // Example: if oldVersion < "1.1.0" { ... }
        
        savePreferences()
    }
}

// MARK: - Validation Errors

enum PreferenceValidationError: LocalizedError {
    case invalidServerURL
    case invalidTimeout
    case invalidRetryAttempts
    case invalidRefreshInterval
    case invalidLogEntries
    case invalidCacheSize
    
    var errorDescription: String? {
        switch self {
        case .invalidServerURL:
            return "Server URL cannot be empty"
        case .invalidTimeout:
            return "Connection timeout must be greater than 0"
        case .invalidRetryAttempts:
            return "Retry attempts cannot be negative"
        case .invalidRefreshInterval:
            return "Auto-refresh interval must be at least 5 seconds"
        case .invalidLogEntries:
            return "Maximum log entries must be at least 100"
        case .invalidCacheSize:
            return "Cache size must be at least 10 MB"
        }
    }
}

// MARK: - Notification Extensions

extension Notification.Name {
    static let preferencesDidChange = Notification.Name("preferencesDidChange")
    static let appearanceDidChange = Notification.Name("appearanceDidChange")
}

// MARK: - Preferences Binding Helpers

extension PreferencesManager {
    func binding<T>(for keyPath: WritableKeyPath<AppPreferences, T>) -> Binding<T> {
        Binding(
            get: { self.preferences[keyPath: keyPath] },
            set: { self.preferences[keyPath: keyPath] = $0 }
        )
    }
    
    func binding<T>(for keyPath: WritableKeyPath<AppPreferences, T>, transaction: Transaction) -> Binding<T> {
        Binding(
            get: { self.preferences[keyPath: keyPath] },
            set: { newValue in
                withTransaction(transaction) {
                    self.preferences[keyPath: keyPath] = newValue
                }
            }
        )
    }
}

// MARK: - Environment Key

struct PreferencesManagerKey: EnvironmentKey {
    static let defaultValue = PreferencesManager.shared
}

extension EnvironmentValues {
    var preferencesManager: PreferencesManager {
        get { self[PreferencesManagerKey.self] }
        set { self[PreferencesManagerKey.self] = newValue }
    }
}

// MARK: - Preferences View Modifier

struct PreferencesViewModifier: ViewModifier {
    @StateObject private var preferencesManager = PreferencesManager.shared
    
    func body(content: Content) -> some View {
        content
            .environmentObject(preferencesManager)
            .environment(\.preferencesManager, preferencesManager)
            .onAppear {
                preferencesManager.migratePreferencesIfNeeded()
            }
    }
}

extension View {
    func withPreferences() -> some View {
        modifier(PreferencesViewModifier())
    }
}

// MARK: - Preferences Validation View

struct PreferencesValidationView: View {
    @EnvironmentObject private var preferencesManager: PreferencesManager
    @State private var validationErrors: [PreferenceValidationError] = []
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            if !validationErrors.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.orange)
                        Text("Validation Errors")
                            .font(.headline)
                    }
                    
                    ForEach(validationErrors, id: \.localizedDescription) { error in
                        Text("• \(error.localizedDescription)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding()
                .background(Color.orange.opacity(0.1))
                .cornerRadius(8)
            }
        }
        .onAppear {
            validatePreferences()
        }
        .onChange(of: preferencesManager.preferences) { _ in
            validatePreferences()
        }
    }
    
    private func validatePreferences() {
        validationErrors = preferencesManager.validatePreferences()
    }
}

// MARK: - Preferences Search

struct PreferenceSearchResult {
    let title: String
    let category: String
    let keyPath: String
    let description: String
}

class PreferencesSearchManager {
    static let shared = PreferencesSearchManager()
    
    private let searchablePreferences: [PreferenceSearchResult] = [
        PreferenceSearchResult(
            title: "Launch at Login",
            category: "General",
            keyPath: "general.launchAtLogin",
            description: "Automatically start the app when you log in"
        ),
        PreferenceSearchResult(
            title: "Server URL",
            category: "Connection",
            keyPath: "connection.serverURL",
            description: "The URL of your WOW server"
        ),
        PreferenceSearchResult(
            title: "Notifications",
            category: "Notifications",
            keyPath: "notifications.enabled",
            description: "Enable or disable notifications"
        ),
        PreferenceSearchResult(
            title: "Theme",
            category: "Appearance",
            keyPath: "appearance.theme",
            description: "Choose between light, dark, or system theme"
        ),
        PreferenceSearchResult(
            title: "Debug Mode",
            category: "Advanced",
            keyPath: "advanced.enableDebugMode",
            description: "Enable debug logging and additional diagnostics"
        )
    ]
    
    private init() {}
    
    func search(_ query: String) -> [PreferenceSearchResult] {
        guard !query.isEmpty else { return [] }
        
        let lowercaseQuery = query.lowercased()
        
        return searchablePreferences.filter { preference in
            preference.title.lowercased().contains(lowercaseQuery) ||
            preference.category.lowercased().contains(lowercaseQuery) ||
            preference.description.lowercased().contains(lowercaseQuery)
        }
    }
}

#Preview {
    VStack {
        Text("Preferences Manager")
            .font(.title)
        
        PreferencesValidationView()
    }
    .padding()
    .withPreferences()
}