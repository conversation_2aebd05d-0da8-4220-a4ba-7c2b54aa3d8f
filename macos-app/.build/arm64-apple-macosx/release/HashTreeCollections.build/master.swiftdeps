version: "Apple Swift version 6.1.2 (swiftlang-6.1.2.1.2 clang-1700.0.13.5)"
options: "41cc076dee231cb650436e4b6243dd8f22fdd95109293712abcda88795e2c166"
build_start_time: [1750467415, 669114000]
build_end_time: [1750467435, 170369000]
inputs:
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_AncestorHashSlots.swift"
  : [1750462297, 866826382]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_Bitmap.swift": [1750462297, 866958006]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_Bucket.swift": [1750462297, 867285131]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_Hash.swift": [1750462297, 867380214]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashLevel.swift": [1750462297, 868059422]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Builder.swift"
  : [1750462297, 868290255]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Debugging.swift"
  : [1750462297, 868440963]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Initializers.swift"
  : [1750462297, 868583921]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Invariants.swift"
  : [1750462297, 868885129]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Lookups.swift"
  : [1750462297, 869126128]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Primitive Insertions.swift"
  : [1750462297, 869237503]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Primitive Removals.swift"
  : [1750462297, 869396003]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Primitive Replacement.swift"
  : [1750462297, 869507753]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Storage.swift"
  : [1750462297, 870134460]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Structural compactMapValues.swift"
  : [1750462297, 870685459]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Structural filter.swift"
  : [1750462297, 870800626]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Structural intersection.swift"
  : [1750462297, 870958709]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Structural isDisjoint.swift"
  : [1750462297, 871069709]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Structural isEqualSet.swift"
  : [1750462297, 871172125]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Structural isSubset.swift"
  : [1750462297, 871484708]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Structural mapValues.swift"
  : [1750462297, 871691583]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Structural merge.swift"
  : [1750462297, 871897582]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Structural subtracting.swift"
  : [1750462297, 872087207]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Structural symmetricDifference.swift"
  : [1750462297, 872284707]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Structural union.swift"
  : [1750462297, 872648373]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Subtree Insertions.swift"
  : [1750462297, 872807581]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Subtree Modify.swift"
  : [1750462297, 872976497]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+Subtree Removals.swift"
  : [1750462297, 873127039]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode+UnsafeHandle.swift"
  : [1750462297, 873270330]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNode.swift": [1750462297, 873417580]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashNodeHeader.swift"
  : [1750462297, 873500247]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashSlot.swift": [1750462297, 873576372]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashStack.swift": [1750462297, 873663663]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashTreeIterator.swift"
  : [1750462297, 873749371]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_HashTreeStatistics.swift"
  : [1750462297, 873884371]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_RawHashNode+UnsafeHandle.swift"
  : [1750462297, 873989829]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_RawHashNode.swift": [1750462297, 874082121]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_UnmanagedHashNode.swift"
  : [1750462297, 875743910]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/HashNode/_UnsafePath.swift": [1750462297, 877451866]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+Codable.swift"
  : [1750462297, 879043363]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+Collection.swift"
  : [1750462297, 879198113]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+CustomReflectable.swift"
  : [1750462297, 879273655]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+Debugging.swift"
  : [1750462297, 879353696]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+Descriptions.swift"
  : [1750462297, 879431988]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+Equatable.swift"
  : [1750462297, 879523738]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+ExpressibleByDictionaryLiteral.swift"
  : [1750462297, 879601363]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+Filter.swift"
  : [1750462297, 879743362]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+Hashable.swift"
  : [1750462297, 879831196]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+Initializers.swift"
  : [1750462297, 880007862]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+Keys.swift"
  : [1750462297, 880154695]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+MapValues.swift"
  : [1750462297, 880241487]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+Merge.swift"
  : [1750462297, 880387861]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+Sendable.swift"
  : [1750462297, 880468278]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+Sequence.swift"
  : [1750462297, 880560819]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary+Values.swift"
  : [1750462297, 880702319]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeDictionary/TreeDictionary.swift"
  : [1750462297, 880823819]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+Codable.swift": [1750462297, 881013485]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+Collection.swift"
  : [1750462297, 881155110]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+CustomReflectable.swift"
  : [1750462297, 881242610]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+Debugging.swift"
  : [1750462297, 881330110]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+Descriptions.swift"
  : [1750462297, 881408652]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+Equatable.swift"
  : [1750462297, 881489735]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+ExpressibleByArrayLiteral.swift"
  : [1750462297, 881572943]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+Extras.swift": [1750462297, 881671734]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+Filter.swift": [1750462297, 881765734]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+Hashable.swift"
  : [1750462297, 881847693]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+Sendable.swift"
  : [1750462297, 881929401]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+Sequence.swift"
  : [1750462297, 882036234]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra Initializers.swift"
  : [1750462297, 882135859]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra basics.swift"
  : [1750462297, 882272484]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra formIntersection.swift"
  : [1750462297, 882399358]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra formSymmetricDifference.swift"
  : [1750462297, 882506817]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra formUnion.swift"
  : [1750462297, 882604900]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra intersection.swift"
  : [1750462297, 882853983]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra isDisjoint.swift"
  : [1750462297, 883933439]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra isEqualSet.swift"
  : [1750462297, 884269897]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra isStrictSubset.swift"
  : [1750462297, 884509147]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra isStrictSuperset.swift"
  : [1750462297, 884906396]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra isSubset.swift"
  : [1750462297, 885131563]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra isSuperset.swift"
  : [1750462297, 885233604]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra subtract.swift"
  : [1750462297, 885328771]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra subtracting.swift"
  : [1750462297, 885431229]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra symmetricDifference.swift"
  : [1750462297, 885563937]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet+SetAlgebra union.swift"
  : [1750462297, 886144644]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/HashTreeCollections/TreeSet/TreeSet.swift": [1750462297, 886387852]
