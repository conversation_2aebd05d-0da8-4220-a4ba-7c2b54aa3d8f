version: "Apple Swift version 6.1.2 (swiftlang-6.1.2.1.2 clang-1700.0.13.5)"
options: "798f3aa35cae187207f4fa5b5572fc5dba17570709436ee8de82ee5ffd36d33e"
build_start_time: [1750467423, 152153000]
build_end_time: [1750467446, 716287000]
inputs:
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncAdjacentPairsSequence.swift"
  : [1750462295, 577950987]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncBufferedByteIterator.swift"
  : [1750462295, 580491066]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncChain2Sequence.swift": [1750462295, 580596774]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncChain3Sequence.swift": [1750462295, 580683108]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncChunkedByGroupSequence.swift"
  : [1750462295, 580819107]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncChunkedOnProjectionSequence.swift"
  : [1750462295, 580933649]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncChunksOfCountOrSignalSequence.swift"
  : [1750462295, 581077357]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncChunksOfCountSequence.swift"
  : [1750462295, 581169274]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncCompactedSequence.swift": [1750462295, 581288607]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncExclusiveReductionsSequence.swift"
  : [1750462295, 581441607]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncInclusiveReductionsSequence.swift"
  : [1750462295, 581536440]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncJoinedBySeparatorSequence.swift"
  : [1750462295, 581672023]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncJoinedSequence.swift": [1750462295, 581753689]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncRemoveDuplicatesSequence.swift"
  : [1750462295, 581891689]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncSyncSequence.swift": [1750462295, 581984522]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncThrottleSequence.swift": [1750462295, 582341438]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncThrowingExclusiveReductionsSequence.swift"
  : [1750462295, 582695313]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncThrowingInclusiveReductionsSequence.swift"
  : [1750462295, 583156312]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/AsyncTimerSequence.swift": [1750462295, 583432395]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Buffer/AsyncBufferSequence.swift"
  : [1750462295, 583714978]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Buffer/BoundedBufferStateMachine.swift"
  : [1750462295, 583947936]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Buffer/BoundedBufferStorage.swift"
  : [1750462295, 584123852]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Buffer/UnboundedBufferStateMachine.swift"
  : [1750462295, 584412310]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Buffer/UnboundedBufferStorage.swift"
  : [1750462295, 584588768]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Channels/AsyncChannel.swift": [1750462295, 584917393]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Channels/AsyncThrowingChannel.swift"
  : [1750462295, 585064768]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Channels/ChannelStateMachine.swift"
  : [1750462295, 585236184]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Channels/ChannelStorage.swift"
  : [1750462295, 585379767]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/CombineLatest/AsyncCombineLatest2Sequence.swift"
  : [1750462295, 585536934]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/CombineLatest/AsyncCombineLatest3Sequence.swift"
  : [1750462295, 585638350]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/CombineLatest/CombineLatestStateMachine.swift"
  : [1750462295, 585809892]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/CombineLatest/CombineLatestStorage.swift"
  : [1750462295, 586090766]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Debounce/AsyncDebounceSequence.swift"
  : [1750462295, 586271516]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Debounce/DebounceStateMachine.swift"
  : [1750462295, 586389099]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Debounce/DebounceStorage.swift"
  : [1750462295, 586561849]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Dictionary.swift": [1750462295, 586660557]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Interspersed/AsyncInterspersedSequence.swift"
  : [1750462295, 586977140]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Locking.swift": [1750462295, 587150306]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Merge/AsyncMerge2Sequence.swift"
  : [1750462295, 587304973]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Merge/AsyncMerge3Sequence.swift"
  : [1750462295, 587417598]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Merge/MergeStateMachine.swift"
  : [1750462295, 587538139]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Merge/MergeStorage.swift": [1750462295, 587687805]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/RangeReplaceableCollection.swift"
  : [1750462295, 587777222]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Rethrow.swift": [1750462295, 587857555]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/SetAlgebra.swift": [1750462295, 587947597]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/UnsafeTransfer.swift": [1750462295, 588042180]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Zip/AsyncZip2Sequence.swift": [1750462295, 588166555]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Zip/AsyncZip3Sequence.swift": [1750462295, 588238305]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Zip/ZipStateMachine.swift": [1750462295, 588331929]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-async-algorithms/Sources/AsyncAlgorithms/Zip/ZipStorage.swift": [1750462295, 588463888]
