version: "Apple Swift version 6.1.2 (swiftlang-6.1.2.1.2 clang-1700.0.13.5)"
options: "4eed2e2f1b245c19244540aa0d22ef77ea322850e76054b57ee63ad67c3f071f"
build_start_time: [1750468290, 92922000]
build_end_time: [1750468311, 322839000]
inputs:
  ? "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/DGCharts.build/DerivedSources/resource_bundle_accessor.swift"
  : [1750467398, 374312401]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Animation/Animator.swift": [**********, 273455646]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Animation/ChartAnimationEasing.swift": [**********, 273615021]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/BarChartView.swift": [**********, 274349103]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/BarLineChartViewBase.swift": [**********, 274687936]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/BubbleChartView.swift": [**********, 274808852]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/CandleStickChartView.swift": [**********, 275599101]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/ChartViewBase.swift": [**********, 277070724]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/CombinedChartView.swift": [**********, 277367848]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/HorizontalBarChartView.swift": [**********, 277537056]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/LineChartView.swift": [**********, 277613556]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/PieChartView.swift": [**********, 277721181]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/PieRadarChartViewBase.swift": [**********, 277843098]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/RadarChartView.swift": [**********, 278190764]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Charts/ScatterChartView.swift": [**********, 278331097]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/AxisBase.swift": [**********, 279298304]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/ChartLimitLine.swift": [**********, 281520009]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/ComponentBase.swift": [**********, 281613009]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/Description.swift": [**********, 281744050]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/Legend.swift": [**********, 281914341]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/LegendEntry.swift": [**********, 282245383]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/Marker.swift": [**********, 282342008]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/MarkerImage.swift": [**********, 282432674]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/MarkerView.swift": [**********, 282515841]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/XAxis.swift": [**********, 282604215]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Components/YAxis.swift": [**********, 284663046]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/ChartBaseDataSet.swift": [**********, 285100295]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/BarChartData.swift": [**********, 285284878]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/BarChartDataEntry.swift": [**********, 285422086]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/BarChartDataSet.swift": [**********, 285562044]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/BarLineScatterCandleBubbleChartData.swift"
  : [**********, 285660878]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/BarLineScatterCandleBubbleChartDataSet.swift"
  : [**********, 285748044]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/BubbleChartData.swift": [**********, 285827711]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/BubbleChartDataEntry.swift"
  : [**********, 285936544]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/BubbleChartDataSet.swift": [**********, 286094627]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/CandleChartData.swift": [**********, 286181168]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/CandleChartDataEntry.swift"
  : [**********, 286267752]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/CandleChartDataSet.swift": [**********, 286364835]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/ChartData.swift": [**********, 286483668]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/ChartDataEntry.swift": [**********, 286731084]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/ChartDataEntryBase.swift": [**********, 287137125]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/ChartDataSet.swift": [**********, 287493041]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/CombinedChartData.swift": [**********, 288404498]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/LineChartData.swift": [**********, 288536248]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/LineChartDataSet.swift": [**********, 288671956]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/LineRadarChartDataSet.swift"
  : [**********, 288760415]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/LineScatterCandleRadarChartDataSet.swift"
  : [**********, 288845373]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/PieChartData.swift": [**********, 288927998]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/PieChartDataEntry.swift": [**********, 289005706]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/PieChartDataSet.swift": [**********, 289101206]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/RadarChartData.swift": [**********, 289176622]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/RadarChartDataEntry.swift"
  : [**********, 289261331]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/RadarChartDataSet.swift": [**********, 289339247]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/ScatterChartData.swift": [**********, 289429789]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Implementations/Standard/ScatterChartDataSet.swift"
  : [**********, 289512330]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/BarChartDataSetProtocol.swift": [**********, 289653413]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/BarLineScatterCandleBubbleChartDataSetProtocol.swift"
  : [**********, 289749746]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/BubbleChartDataSetProtocol.swift": [**********, 289817163]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/CandleChartDataSetProtocol.swift": [**********, 289880538]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/ChartDataSetProtocol.swift": [**********, 290051288]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/LineChartDataSetProtocol.swift": [**********, 290132871]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/LineRadarChartDataSetProtocol.swift": [**********, 290214079]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/LineScatterCandleRadarChartDataSetProtocol.swift"
  : [**********, 290298996]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/PieChartDataSetProtocol.swift": [**********, 290378371]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/RadarChartDataSetProtocol.swift": [**********, 290458120]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Data/Interfaces/ScatterChartDataSetProtocol.swift": [**********, *********]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Filters/DataApproximator+N.swift": [**********, *********]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Filters/DataApproximator.swift": [**********, *********]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Formatters/AxisValueFormatter.swift": [**********, *********]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Formatters/DefaultAxisValueFormatter.swift": [**********, *********]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Formatters/DefaultFillFormatter.swift": [**********, *********]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Formatters/DefaultValueFormatter.swift": [**********, *********]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Formatters/FillFormatter.swift": [**********, *********]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Formatters/IndexAxisValueFormatter.swift": [**********, *********]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Formatters/ValueFormatter.swift": [**********, *********]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/BarHighlighter.swift": [**********, *********]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/ChartHighlighter.swift": [**********, *********]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/CombinedHighlighter.swift": [**********, 293506782]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/Highlight.swift": [**********, 293642782]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/Highlighter.swift": [**********, 293728782]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/HorizontalBarHighlighter.swift": [**********, 294019615]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/PieHighlighter.swift": [**********, 294115698]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/PieRadarHighlighter.swift": [**********, 294204990]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/RadarHighlighter.swift": [**********, 294305573]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Highlight/Range.swift": [**********, 294476906]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Interfaces/BarChartDataProvider.swift": [**********, 294771364]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Interfaces/BarLineScatterCandleBubbleChartDataProvider.swift"
  : [**********, 294931530]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Interfaces/BubbleChartDataProvider.swift": [**********, 295012280]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Interfaces/CandleChartDataProvider.swift": [**********, 295091488]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Interfaces/ChartDataProvider.swift": [**********, 295168572]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Interfaces/CombinedChartDataProvider.swift": [**********, 295250071]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Interfaces/LineChartDataProvider.swift": [**********, 295332488]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Interfaces/ScatterChartDataProvider.swift": [**********, 295410613]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Jobs/AnimatedMoveViewJob.swift": [**********, 295559488]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Jobs/AnimatedViewPortJob.swift": [**********, 295645154]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Jobs/AnimatedZoomViewJob.swift": [**********, 295712613]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Jobs/MoveViewJob.swift": [**********, 295781279]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Jobs/ViewPortJob.swift": [**********, 295865571]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Jobs/ZoomViewJob.swift": [**********, 295944904]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/AxisRenderer.swift": [**********, 296172278]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/BarChartRenderer.swift": [**********, 296325070]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/BarLineScatterCandleBubbleRenderer.swift": [**********, 296503195]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/BubbleChartRenderer.swift": [**********, 296703028]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/CandleStickChartRenderer.swift": [**********, 296973152]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/CombinedChartRenderer.swift": [**********, 297329360]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/DataRenderer.swift": [**********, 297468401]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/HorizontalBarChartRenderer.swift": [**********, 297759151]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/LegendRenderer.swift": [**********, 297928526]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/LineChartRenderer.swift": [**********, 298026859]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/LineRadarRenderer.swift": [**********, 298118317]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/LineScatterCandleRadarRenderer.swift": [**********, 298202650]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/PieChartRenderer.swift": [**********, 298332275]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/RadarChartRenderer.swift": [**********, 298452442]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/Renderer.swift": [**********, 298548775]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/Scatter/ChevronDownShapeRenderer.swift": [**********, 298676275]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/Scatter/ChevronUpShapeRenderer.swift": [**********, 298751108]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/Scatter/CircleShapeRenderer.swift": [**********, 298824149]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/Scatter/CrossShapeRenderer.swift": [**********, 298891566]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/Scatter/ShapeRenderer.swift": [**********, 298962024]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/Scatter/SquareShapeRenderer.swift": [**********, 299037483]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/Scatter/TriangleShapeRenderer.swift": [**********, 299106691]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/Scatter/XShapeRenderer.swift": [**********, 299172649]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/ScatterChartRenderer.swift": [**********, 299342232]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/XAxisRenderer.swift": [**********, 299574273]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/XAxisRendererHorizontalBarChart.swift": [**********, 301451979]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/XAxisRendererRadarChart.swift": [**********, 301624770]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/YAxisRenderer.swift": [**********, 302559810]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/YAxisRendererHorizontalBarChart.swift": [**********, 303495309]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Renderers/YAxisRendererRadarChart.swift": [**********, 303729142]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/ChartColorTemplates.swift": [**********, 304020767]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/ChartUtils.swift": [**********, 304240266]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Fill.swift": [**********, 307094054]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Indexed.swift": [**********, 307199054]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Partition.swift": [**********, 307344512]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Platform+Accessibility.swift": [**********, 307492553]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Platform+Color.swift": [**********, 307582928]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Platform+Gestures.swift": [**********, 307719094]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Platform+Graphics.swift": [**********, 307934636]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Platform+Touch Handling.swift": [**********, 308431927]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Platform.swift": [**********, 308600801]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Sequence+KeyPath.swift": [**********, 309007509]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/Transformer.swift": [**********, 309196009]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/TransformerHorizontalBarChart.swift": [**********, 309582675]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/Charts/Source/Charts/Utils/ViewPortHandler.swift": [**********, 309760425]
