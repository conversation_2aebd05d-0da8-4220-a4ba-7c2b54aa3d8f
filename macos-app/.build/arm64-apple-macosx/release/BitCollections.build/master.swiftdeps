version: "Apple Swift version 6.1.2 (swiftlang-6.1.2.1.2 clang-1700.0.13.5)"
options: "293f6d45d0b7bf4e75c8cdbf90202f828cad2244e4532134bf6f7a0282b50a82"
build_start_time: [1750468295, 67809000]
build_end_time: [1750468298, 900875000]
inputs:
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+BitwiseOperations.swift"
  : [1750462297, 833150224]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+ChunkedBitsIterators.swift"
  : [1750462297, 834712097]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+Codable.swift": [1750462297, 834996555]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+Collection.swift": [1750462297, 835407012]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+Copy.swift": [1750462297, 835667970]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+CustomReflectable.swift"
  : [1750462297, 836063136]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+Descriptions.swift"
  : [1750462297, 836169345]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+Equatable.swift": [1750462297, 836267511]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+ExpressibleByArrayLiteral.swift"
  : [1750462297, 836371178]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+ExpressibleByStringLiteral.swift"
  : [1750462297, 836451511]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+Extras.swift": [1750462297, 836529594]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+Fill.swift": [1750462297, 836609385]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+Hashable.swift": [1750462297, 836720885]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+Initializers.swift"
  : [1750462297, 836876635]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+Invariants.swift": [1750462297, 836972552]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+LosslessStringConvertible.swift"
  : [1750462297, 837070718]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+RandomBits.swift": [1750462297, 837150218]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+RangeReplaceableCollection.swift"
  : [1750462297, 837262551]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+Shifts.swift": [1750462297, 837421968]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+Testing.swift": [1750462297, 837503926]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray._UnsafeHandle.swift"
  : [1750462297, 837631884]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray.swift": [1750462297, 837711801]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+BidirectionalCollection.swift"
  : [1750462297, 839854256]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+Codable.swift": [1750462297, 839955464]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+CustomDebugStringConvertible.swift"
  : [1750462297, 840052797]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+CustomReflectable.swift"
  : [1750462297, 840134714]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+CustomStringConvertible.swift"
  : [1750462297, 840239380]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+Equatable.swift": [1750462297, 840363213]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+ExpressibleByArrayLiteral.swift"
  : [1750462297, 840447130]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+Extras.swift": [1750462297, 840590713]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+Hashable.swift": [1750462297, 840666296]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+Initializers.swift": [1750462297, 842103419]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+Invariants.swift": [1750462297, 842225919]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+Random.swift": [1750462297, 847854077]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra basics.swift"
  : [1750462297, 848093243]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra conformance.swift"
  : [1750462297, 848618492]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra formIntersection.swift"
  : [1750462297, 852562736]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra formSymmetricDifference.swift"
  : [1750462297, 853059111]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra formUnion.swift"
  : [1750462297, 853242360]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra intersection.swift"
  : [1750462297, 853350985]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra isDisjoint.swift"
  : [1750462297, 853454402]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra isEqualSet.swift"
  : [1750462297, 853551652]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra isStrictSubset.swift"
  : [1750462297, 853706860]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra isStrictSuperset.swift"
  : [1750462297, 853829151]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra isSubset.swift"
  : [1750462297, 853973859]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra isSuperset.swift"
  : [1750462297, 854062943]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra subtract.swift"
  : [1750462297, 854330192]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra subtracting.swift"
  : [1750462297, 854620108]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra symmetricDifference.swift"
  : [1750462297, 854745608]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra union.swift"
  : [1750462297, 854834941]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+Sorted Collection APIs.swift"
  : [1750462297, 855082983]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet.Counted.swift": [1750462297, 855666815]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet.Index.swift": [1750462297, 855794523]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet._UnsafeHandle.swift": [1750462297, 855951732]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet.swift": [1750462297, 856074523]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/Shared/Range+Utilities.swift": [1750462297, 856381856]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/Shared/Slice+Utilities.swift": [1750462297, 856473897]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/Shared/UInt+Tricks.swift": [1750462297, 856566189]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/BitCollections/Shared/_Word.swift": [1750462297, 856642147]
