version: "Apple Swift version 6.1.2 (swiftlang-6.1.2.1.2 clang-1700.0.13.5)"
options: "7942881d3acc2d8cd34ea8fe3e7852fdfd7c1a11e870686e917fc875bc3b2cf9"
build_start_time: [1750467404, 833639000]
build_end_time: [1750467415, 475310000]
inputs:
  "/Users/<USER>/w-o-w/macos-app/.build/arm64-apple-macosx/release/Crypto.build/DerivedSources/resource_bundle_accessor.swift": [1750467398, 370512372]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/AEADs/AES/GCM/AES-GCM.swift": [1750462296, 564794754]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/AEADs/AES/GCM/BoringSSL/AES-GCM_boring.swift": [1750462296, 564951046]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/AEADs/ChachaPoly/BoringSSL/ChaChaPoly_boring.swift"
  : [1750462296, 565179920]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/AEADs/ChachaPoly/ChaChaPoly.swift": [1750462296, 565302670]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/AEADs/Cipher.swift": [1750462296, 565398962]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/AEADs/Nonces.swift": [1750462296, 565531795]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/ASN1.swift": [1750462296, 566543543]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/Basic ASN1 Types/ASN1Any.swift": [1750462296, 567106126]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/Basic ASN1 Types/ASN1BitString.swift": [1750462296, 567240334]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/Basic ASN1 Types/ASN1Boolean.swift": [1750462296, 567355375]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/Basic ASN1 Types/ASN1Identifier.swift": [1750462296, 567505459]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/Basic ASN1 Types/ASN1Integer.swift": [1750462296, 567639708]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/Basic ASN1 Types/ASN1Null.swift": [1750462296, 567729083]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/Basic ASN1 Types/ASN1OctetString.swift": [1750462296, 567814958]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/Basic ASN1 Types/ASN1Strings.swift": [1750462296, 567950750]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/Basic ASN1 Types/ArraySliceBigint.swift": [1750462296, 568039499]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/Basic ASN1 Types/GeneralizedTime.swift": [1750462296, 568366957]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/Basic ASN1 Types/ObjectIdentifier.swift": [1750462296, 568585999]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/ECDSASignature.swift": [1750462296, 568721832]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/PEMDocument.swift": [1750462296, 568871540]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/PKCS8PrivateKey.swift": [1750462296, 569027123]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/SEC1PrivateKey.swift": [1750462296, 569194456]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/ASN1/SubjectPublicKeyInfo.swift": [1750462296, 569336831]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/CryptoKitErrors.swift": [1750462296, 569825455]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Digests/BoringSSL/Digest_boring.swift": [1750462296, 570071371]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Digests/Digest.swift": [1750462296, 570155455]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Digests/Digests.swift": [1750462296, 570340496]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Digests/HashFunctions.swift": [1750462296, 570860954]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Digests/HashFunctions_SHA2.swift": [1750462296, 570998370]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/Ciphersuite/HPKE-AEAD.swift": [1750462296, 571383786]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/Ciphersuite/HPKE-Ciphersuite.swift": [1750462296, 571471953]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/Ciphersuite/HPKE-KDF.swift": [1750462296, 571611786]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/Ciphersuite/HPKE-KexKeyDerivation.swift": [1750462296, 571708536]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/Ciphersuite/HPKE-LabeledExtract.swift": [1750462296, 571790494]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/Ciphersuite/HPKE-Utils.swift": [1750462296, 571871077]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/Ciphersuite/KEM/Conformances/DHKEM.swift": [1750462296, 572098952]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/Ciphersuite/KEM/Conformances/HPKE-KEM-Curve25519.swift"
  : [1750462296, 572178327]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/Ciphersuite/KEM/Conformances/HPKE-NIST-EC-KEMs.swift"
  : [1750462296, 572313785]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/Ciphersuite/KEM/HPKE-KEM.swift": [1750462296, 572388868]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/HPKE-Errors.swift": [1750462296, 572464451]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/HPKE.swift": [1750462296, 572558326]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/Key Schedule/HPKE-Context.swift": [1750462296, 572688576]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/Key Schedule/HPKE-KeySchedule.swift": [1750462296, 572813617]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/HPKE/Modes/HPKE-Modes.swift": [1750462296, 572951200]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Insecure/Insecure.swift": [1750462296, 573061867]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Insecure/Insecure_HashFunctions.swift": [1750462296, 573339450]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/KEM/KEM.swift": [1750462296, 573478616]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Key Agreement/BoringSSL/ECDH_boring.swift": [1750462296, 573650491]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Key Agreement/DH.swift": [1750462296, 573791449]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Key Agreement/ECDH.swift": [1750462296, 574013157]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Key Derivation/HKDF.swift": [1750462296, 574871073]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Key Wrapping/AESWrap.swift": [1750462296, 576850736]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Key Wrapping/BoringSSL/AESWrap_boring.swift": [1750462296, 577209444]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Keys/EC/BoringSSL/Ed25519_boring.swift": [1750462296, 577533152]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Keys/EC/BoringSSL/NISTCurvesKeys_boring.swift": [1750462296, 578492484]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Keys/EC/BoringSSL/X25519Keys_boring.swift": [1750462296, 578746608]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Keys/EC/Curve25519.swift": [1750462296, 578851108]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Keys/EC/Ed25519Keys.swift": [1750462296, 578995858]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Keys/EC/NISTCurvesKeys.swift": [1750462296, 579092733]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Keys/EC/X25519Keys.swift": [1750462296, 579240608]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Keys/Symmetric/SymmetricKeys.swift": [1750462296, 579435399]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Message Authentication Codes/HMAC/HMAC.swift": [1750462296, 579701232]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Message Authentication Codes/MACFunctions.swift": [1750462296, 579782398]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Message Authentication Codes/MessageAuthenticationCode.swift"
  : [1750462296, 579865273]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/PRF/AES.swift": [1750462296, 580002731]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Signatures/BoringSSL/ECDSASignature_boring.swift"
  : [1750462296, 580417106]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Signatures/BoringSSL/ECDSA_boring.swift": [1750462296, 580549522]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Signatures/BoringSSL/EdDSA_boring.swift": [1750462296, 580681189]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Signatures/ECDSA.swift": [1750462296, 580834063]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Signatures/Ed25519.swift": [1750462296, 581383146]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Signatures/Signature.swift": [1750462296, 581459854]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Util/BoringSSL/CryptoKitErrors_boring.swift": [1750462296, 581676229]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Util/BoringSSL/RNG_boring.swift": [1750462296, 581770312]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Util/BoringSSL/SafeCompare_boring.swift": [1750462296, 581878937]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Util/BoringSSL/Zeroization_boring.swift": [1750462296, 582046270]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Util/PrettyBytes.swift": [1750462296, 582158645]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Util/SafeCompare.swift": [1750462296, 582251770]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Util/SecureBytes.swift": [1750462296, 582356519]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-crypto/Sources/Crypto/Util/Zeroization.swift": [1750462296, 582455728]
