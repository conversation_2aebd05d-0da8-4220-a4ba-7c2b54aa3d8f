version: "Apple Swift version 6.1.2 (swiftlang-6.1.2.1.2 clang-1700.0.13.5)"
options: "1f92a92c2847a63c6398799ff49e724bb0bd28db9f0cc270bba6f1fac85e3cce"
build_start_time: [1750468291, 840404000]
build_end_time: [1750468303, 866535000]
inputs:
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Basics/BigString+Builder.swift"
  : [1750462297, 928704580]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Basics/BigString+Contents.swift"
  : [1750462297, 928807955]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Basics/BigString+Debugging.swift"
  : [1750462297, 928904372]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Basics/BigString+Index.swift": [1750462297, 929046080]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Basics/BigString+Ingester.swift"
  : [1750462297, 929203705]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Basics/BigString+Invariants.swift"
  : [1750462297, 929282246]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Basics/BigString+Iterators.swift"
  : [1750462297, 929369746]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Basics/BigString+Metrics.swift"
  : [1750462297, 929503496]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Basics/BigString+Summary.swift"
  : [1750462297, 929579829]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Basics/BigString.swift": [1750462297, 929703496]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Chunk/BigString+Chunk+Append and Insert.swift"
  : [1750462297, 929896787]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Chunk/BigString+Chunk+Breaks.swift"
  : [1750462297, 930079453]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Chunk/BigString+Chunk+Counts.swift"
  : [1750462297, 930217495]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Chunk/BigString+Chunk+Description.swift"
  : [1750462297, 930303245]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Chunk/BigString+Chunk+Indexing by Characters.swift"
  : [1750462297, 930390453]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Chunk/BigString+Chunk+Indexing by UTF16.swift"
  : [1750462297, 930471745]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Chunk/BigString+Chunk+RopeElement.swift"
  : [1750462297, 930597578]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Chunk/BigString+Chunk+Splitting.swift"
  : [1750462297, 930718078]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Chunk/BigString+Chunk.swift": [1750462297, 930794536]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Conformances/BigString+BidirectionalCollection.swift"
  : [1750462297, 930930619]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Conformances/BigString+Comparable.swift"
  : [1750462297, 931020369]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Conformances/BigString+CustomDebugStringConvertible.swift"
  : [1750462297, 931089452]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Conformances/BigString+CustomStringConvertible.swift"
  : [1750462297, 931156368]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Conformances/BigString+Equatable.swift"
  : [1750462297, 931235660]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Conformances/BigString+ExpressibleByStringLiteral.swift"
  : [1750462297, 931311118]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Conformances/BigString+Hashing.swift"
  : [1750462297, 931396160]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Conformances/BigString+LosslessStringConvertible.swift"
  : [1750462297, 931473618]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Conformances/BigString+RangeReplaceableCollection.swift"
  : [1750462297, 931605326]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Conformances/BigString+Sequence.swift"
  : [1750462297, 931754201]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Conformances/BigString+TextOutputStream.swift"
  : [1750462297, 931852492]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Operations/BigString+Append.swift"
  : [1750462297, 932056992]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Operations/BigString+Initializers.swift"
  : [1750462297, 932192825]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Operations/BigString+Insert.swift"
  : [1750462297, 932277367]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Operations/BigString+Managing Breaks.swift"
  : [1750462297, 932441450]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Operations/BigString+RemoveSubrange.swift"
  : [1750462297, 932529741]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Operations/BigString+ReplaceSubrange.swift"
  : [1750462297, 932609033]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Operations/BigString+Split.swift"
  : [1750462297, 932685366]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Operations/Range+BigString.swift"
  : [1750462297, 932766574]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Views/BigString+UTF16View.swift"
  : [1750462297, 938154150]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Views/BigString+UTF8View.swift"
  : [1750462297, 938334774]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Views/BigString+UnicodeScalarView.swift"
  : [1750462297, 939090023]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Views/BigSubstring+UTF16View.swift"
  : [1750462297, 940844521]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Views/BigSubstring+UTF8View.swift"
  : [1750462297, 941102770]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Views/BigSubstring+UnicodeScalarView.swift"
  : [1750462297, 945387639]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/BigString/Views/BigSubstring.swift": [1750462297, 945632680]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Rope/Basics/Rope+Builder.swift": [1750462297, 946551804]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Rope/Basics/Rope+Debugging.swift": [1750462297, 946804637]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Rope/Basics/Rope+Invariants.swift": [1750462297, 946981178]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Rope/Basics/Rope+_Node.swift": [1750462297, 947308802]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Rope/Basics/Rope+_Storage.swift": [1750462297, 947508219]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Rope/Basics/Rope+_UnmanagedLeaf.swift": [1750462297, 947681344]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Rope/Basics/Rope+_UnsafeHandle.swift": [1750462297, 947929260]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Rope/Basics/Rope.swift": [1750462297, 948021926]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Rope/Basics/RopeElement.swift": [1750462297, 948103426]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Rope/Basics/RopeMetric.swift": [1750462297, 948194635]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Rope/Basics/RopeSummary.swift": [1750462297, 948287884]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Rope/Basics/_RopeItem.swift": [1750462297, 948364468]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Rope/Basics/_RopePath.swift": [1750462297, 948446259]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Rope/Basics/_RopeVersion.swift": [1750462297, 948535426]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Rope/Conformances/Rope+Collection.swift"
  : [1750462297, 948865050]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Rope/Conformances/Rope+Index.swift": [1750462297, 949898090]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Rope/Conformances/Rope+Sequence.swift": [1750462297, 950289923]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Rope/Operations/Rope+Append.swift": [1750462297, 951076630]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Rope/Operations/Rope+Extract.swift": [1750462297, 951184213]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Rope/Operations/Rope+Find.swift": [1750462297, 951271963]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Rope/Operations/Rope+ForEachWhile.swift"
  : [1750462297, 951396755]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Rope/Operations/Rope+Insert.swift": [1750462297, 951536379]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Rope/Operations/Rope+Join.swift": [1750462297, 951620796]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Rope/Operations/Rope+MutatingForEach.swift"
  : [1750462297, 951712504]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Rope/Operations/Rope+Remove.swift": [1750462297, 951845212]
  ? "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Rope/Operations/Rope+RemoveSubrange.swift"
  : [1750462297, 952000879]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Rope/Operations/Rope+Split.swift": [1750462297, 952099462]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Utilities/Optional Utilities.swift": [1750462297, 952254420]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Utilities/String Utilities.swift": [1750462297, 952399795]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Utilities/String.Index+ABI.swift": [1750462297, 952478795]
  "/Users/<USER>/w-o-w/macos-app/.build/checkouts/swift-collections/Sources/RopeModule/Utilities/_CharacterRecognizer.swift": [1750462297, 952609586]
