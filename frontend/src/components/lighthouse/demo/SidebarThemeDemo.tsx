import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { cn } from '~/lib/ui-utils';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { LighthouseWithSidebarThemes, LighthouseWithFloatingThemes } from '../LighthouseWithSidebarThemes';

// Temporary implementation for demo
const AccessibilityUtils = {
  prefersReducedMotion: () => {
    if (typeof window !== 'undefined') {
      return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    }
    return false;
  }
};

interface SidebarThemeDemoProps {
  className?: string;
}

export function SidebarThemeDemo({ className }: SidebarThemeDemoProps) {
  const [demoMode, setDemoMode] = useState<'sidebar' | 'floating' | 'instructions'>('instructions');
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  if (demoMode === 'instructions') {
    return (
      <div className={cn('min-h-screen bg-background flex items-center justify-center p-4', className)}>
        <motion.div
          initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, scale: 0.9 }}
          animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, scale: 1 }}
          transition={{ duration: prefersReducedMotion ? 0.1 : 0.3 }}
          className="w-full max-w-4xl"
        >
          <Card className="shadow-2xl border">
            <CardHeader className="text-center pb-6">
              <CardTitle className="text-3xl font-bold mb-4">🎨 Sidebar Theme Switcher</CardTitle>
              <p className="text-lg text-muted-foreground">
                The theme switcher is now integrated into the navigation sidebar for a cleaner experience
              </p>
            </CardHeader>
            
            <CardContent className="space-y-8">
              {/* Feature Comparison */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="border-green-200 bg-green-50 dark:bg-green-950 dark:border-green-800">
                  <CardHeader>
                    <CardTitle className="text-lg text-green-800 dark:text-green-200 flex items-center gap-2">
                      ✅ Sidebar Theme Switcher
                      <Badge variant="default" className="bg-green-600">Recommended</Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3 text-sm text-green-700 dark:text-green-300">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                      <span>Integrated into navigation sidebar</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                      <span>Compact dropdown design</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                      <span>No floating elements</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                      <span>Better mobile experience</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                      <span>Consistent with UI patterns</span>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-blue-200 bg-blue-50 dark:bg-blue-950 dark:border-blue-800">
                  <CardHeader>
                    <CardTitle className="text-lg text-blue-800 dark:text-blue-200">
                      💫 Floating Theme Switcher
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3 text-sm text-blue-700 dark:text-blue-300">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full" />
                      <span>Floating button in top-right</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full" />
                      <span>Modal-based theme selection</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full" />
                      <span>More prominent visibility</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full" />
                      <span>Good for demos</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full" />
                      <span>Original implementation</span>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Location Guide */}
              <div className="bg-muted/50 rounded-lg p-6">
                <h3 className="text-lg font-semibold mb-4">📍 Where to Find the Theme Switcher</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium text-green-700 dark:text-green-300 mb-2">Sidebar Version:</h4>
                    <ul className="space-y-1 text-sm text-muted-foreground">
                      <li>• Look in the left navigation sidebar</li>
                      <li>• Find the "Theme" section with colored dots</li>
                      <li>• Click to expand theme options</li>
                      <li>• Located above the Quick Actions</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-medium text-blue-700 dark:text-blue-300 mb-2">Floating Version:</h4>
                    <ul className="space-y-1 text-sm text-muted-foreground">
                      <li>• Look in the top-right corner</li>
                      <li>• Button with 3 colored circles + "Themes"</li>
                      <li>• Click to open modal with all themes</li>
                      <li>• Appears after 1 second delay</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Demo Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  onClick={() => setDemoMode('sidebar')}
                  className="bg-green-600 hover:bg-green-700 text-white"
                  size="lg"
                >
                  Try Sidebar Theme Switcher
                </Button>
                <Button
                  onClick={() => setDemoMode('floating')}
                  variant="outline"
                  className="border-blue-500 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-950"
                  size="lg"
                >
                  Try Floating Theme Switcher
                </Button>
              </div>

              {/* Instructions */}
              <div className="text-center space-y-2">
                <p className="text-sm text-muted-foreground">
                  Choose a demo mode to see the theme switcher in action
                </p>
                <p className="text-xs text-muted-foreground">
                  You can switch between different visual themes and see the changes in real-time
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    );
  }

  if (demoMode === 'sidebar') {
    return (
      <div className="relative">
        <LighthouseWithSidebarThemes defaultTheme="glassmorphism" />
        
        {/* Demo Info Overlay */}
        <div className="fixed top-4 left-4 z-50">
          <Card className="bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800 max-w-sm">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                <span className="text-sm font-medium text-green-800 dark:text-green-200">
                  Sidebar Theme Demo
                </span>
              </div>
              <p className="text-xs text-green-700 dark:text-green-300 mb-3">
                Look for the "Theme" section in the left sidebar with colored dots. Click to expand theme options.
              </p>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setDemoMode('instructions')}
                className="text-xs border-green-300 text-green-700 hover:bg-green-100 dark:border-green-700 dark:text-green-300 dark:hover:bg-green-900"
              >
                Back to Instructions
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (demoMode === 'floating') {
    return (
      <div className="relative">
        <LighthouseWithFloatingThemes defaultTheme="glassmorphism" />
        
        {/* Demo Info Overlay */}
        <div className="fixed top-4 left-4 z-40">
          <Card className="bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800 max-w-sm">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                  Floating Theme Demo
                </span>
              </div>
              <p className="text-xs text-blue-700 dark:text-blue-300 mb-3">
                Look for the "Themes" button in the top-right corner with 3 colored circles. It will appear after 1 second.
              </p>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setDemoMode('instructions')}
                className="text-xs border-blue-300 text-blue-700 hover:bg-blue-100 dark:border-blue-700 dark:text-blue-300 dark:hover:bg-blue-900"
              >
                Back to Instructions
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return null;
}

export default SidebarThemeDemo;
