import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { cn } from '~/lib/ui-utils';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { LighthouseWithThemes } from '../core/LighthouseWithThemes';
import { DESIGN_THEMES, type DesignTheme } from '../design-mockups/theme-config';
// Note: Replace with your actual accessibility utils import path
// import { AccessibilityUtils } from '@/utils/accessibilityUtils';

// Temporary implementation for demo
const AccessibilityUtils = {
  prefersReducedMotion: () => {
    if (typeof window !== 'undefined') {
      return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    }
    return false;
  }
};

interface LighthouseThemeDemoProps {
  className?: string;
}

export function LighthouseThemeDemo({ className }: LighthouseThemeDemoProps) {
  const [selectedTheme, setSelectedTheme] = useState<DesignTheme>('glassmorphism');
  const [showInstructions, setShowInstructions] = useState(true);
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  const handleThemeSelect = (theme: DesignTheme) => {
    setSelectedTheme(theme);
    setShowInstructions(false);
  };

  return (
    <div className={cn('min-h-screen bg-background', className)}>
      {/* Instructions Overlay */}
      {showInstructions && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <motion.div
            className="w-full max-w-4xl bg-background rounded-xl shadow-2xl border p-8"
            initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, scale: 0.9 }}
            animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, scale: 1 }}
            transition={{ duration: prefersReducedMotion ? 0.1 : 0.3 }}
          >
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold mb-4">Welcome to Lighthouse Theme Demo</h1>
              <p className="text-lg text-muted-foreground">
                Experience how different visual themes transform the same powerful AI platform
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {Object.entries(DESIGN_THEMES).map(([themeKey, config], index) => (
                <motion.div
                  key={themeKey}
                  initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, y: 20 }}
                  animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, y: 0 }}
                  transition={{ duration: prefersReducedMotion ? 0.1 : 0.3, delay: index * 0.1 }}
                >
                  <Card
                    className="cursor-pointer transition-all duration-300 hover:shadow-lg hover:border-primary/50"
                    onClick={() => handleThemeSelect(themeKey as DesignTheme)}
                  >
                    {/* Theme Preview */}
                    <div className="h-20 relative overflow-hidden rounded-t-lg">
                      <div
                        className="absolute inset-0"
                        style={{
                          background: `linear-gradient(135deg, ${config.colorPalette[0]} 0%, ${config.colorPalette[1]} 50%, ${config.colorPalette[2] || config.colorPalette[0]} 100%)`
                        }}
                      />
                      
                      {/* Theme-specific preview elements */}
                      {themeKey === 'glassmorphism' && (
                        <div className="absolute inset-0 backdrop-blur-sm bg-white/20">
                          <div className="absolute top-2 left-2 w-3 h-3 bg-white/40 rounded-full" />
                          <div className="absolute bottom-2 right-2 w-4 h-4 bg-white/30 rounded" />
                        </div>
                      )}
                      
                      {themeKey === 'neumorphism' && (
                        <div className="absolute inset-0 bg-gray-100">
                          <div className="absolute top-2 left-2 w-3 h-3 bg-gray-100 rounded-full shadow-inner" />
                          <div className="absolute bottom-2 right-2 w-4 h-4 bg-gray-100 rounded shadow-md" />
                        </div>
                      )}
                      
                      {themeKey === 'cyberpunk' && (
                        <div className="absolute inset-0 bg-black">
                          <div className="absolute top-2 left-2 w-3 h-3 bg-cyan-500 rounded-full animate-pulse" />
                          <div className="absolute bottom-2 right-2 w-4 h-4 border border-purple-500 rounded" />
                        </div>
                      )}
                      
                      {themeKey === 'minimalist' && (
                        <div className="absolute inset-0 bg-white">
                          <div className="absolute top-3 left-3 w-6 h-px bg-gray-300" />
                          <div className="absolute bottom-3 right-3 w-2 h-2 border border-gray-300 rounded-full" />
                        </div>
                      )}
                      
                      {themeKey === 'brutalist' && (
                        <div className="absolute inset-0 bg-yellow-400">
                          <div className="absolute top-2 left-2 w-3 h-3 bg-red-500 border border-black transform rotate-12" />
                          <div className="absolute bottom-2 right-2 w-4 h-4 bg-blue-500 border border-black transform -rotate-12" />
                        </div>
                      )}
                    </div>

                    <CardHeader className="pb-3">
                      <CardTitle className="text-base">{config.name}</CardTitle>
                      <p className="text-sm text-muted-foreground">{config.description}</p>
                    </CardHeader>

                    <CardContent className="pt-0">
                      <div className="space-y-2">
                        <div>
                          <p className="text-xs font-medium text-muted-foreground mb-1">Best for</p>
                          <div className="flex flex-wrap gap-1">
                            {config.bestFor.slice(0, 2).map((use, idx) => (
                              <Badge key={idx} variant="secondary" className="text-xs">
                                {use}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        
                        <div className="flex gap-1 pt-2">
                          {config.colorPalette.slice(0, 4).map((color, idx) => (
                            <div
                              key={idx}
                              className="w-3 h-3 rounded-full border border-border/30"
                              style={{ backgroundColor: color }}
                            />
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>

            <div className="text-center space-y-4">
              <p className="text-sm text-muted-foreground">
                Click any theme above to see it in action, or close this dialog to start with the default theme
              </p>
              <div className="flex justify-center gap-4">
                <Button
                  variant="outline"
                  onClick={() => setShowInstructions(false)}
                >
                  Start with Default
                </Button>
                <Button
                  onClick={() => handleThemeSelect('glassmorphism')}
                >
                  Try Glassmorphism
                </Button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}

      {/* Main Lighthouse Application with Themes */}
      <LighthouseWithThemes
        defaultTheme={selectedTheme}
        showThemeSwitcher={true}
      />

      {/* Demo Info Panel */}
      {!showInstructions && (
        <motion.div
          className="fixed bottom-4 left-4 z-40"
          initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, x: -20 }}
          animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, x: 0 }}
          transition={{ duration: prefersReducedMotion ? 0.1 : 0.3, delay: 1 }}
        >
          <Card className="backdrop-blur-sm bg-background/90 border-border/50 shadow-lg max-w-sm">
            <CardContent className="p-4">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                <span className="text-sm font-medium">Theme Demo Active</span>
              </div>
              <p className="text-xs text-muted-foreground mb-3">
                Current theme: <span className="font-medium">{DESIGN_THEMES[selectedTheme].name}</span>
              </p>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setShowInstructions(true)}
                  className="text-xs"
                >
                  Change Theme
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => {
                    const themes = Object.keys(DESIGN_THEMES) as DesignTheme[];
                    const currentIndex = themes.indexOf(selectedTheme);
                    const nextIndex = (currentIndex + 1) % themes.length;
                    setSelectedTheme(themes[nextIndex]);
                  }}
                  className="text-xs"
                >
                  Next Theme
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  );
}

// Quick Start Component for Easy Integration
export function QuickStartLighthouse() {
  return (
    <LighthouseWithThemes
      defaultTheme="glassmorphism"
      showThemeSwitcher={true}
    />
  );
}

// Themed Lighthouse without switcher for production
export function ProductionLighthouse({ theme = 'glassmorphism' }: { theme?: DesignTheme }) {
  return (
    <LighthouseWithThemes
      defaultTheme={theme}
      showThemeSwitcher={false}
    />
  );
}
