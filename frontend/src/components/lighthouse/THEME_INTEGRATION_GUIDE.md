# 🎨 Lighthouse Theme Integration Guide

## Option 2: Theme Switcher Integration

This guide shows you how to integrate the interactive theme switcher into your existing Lighthouse application, allowing users to choose from 5 different visual themes while preserving all functionality.

## 🚀 Quick Start

### 1. Replace Your Current Lighthouse Component

**Before:**
```tsx
import { LighthouseMain } from '@/components/lighthouse';

function App() {
  return <LighthouseMain initialModule="dashboard" />;
}
```

**After:**
```tsx
import { QuickStartLighthouse } from '@/components/lighthouse';

function App() {
  return <QuickStartLighthouse />;
}
```

### 2. Or Use the Full Demo Experience

```tsx
import { LighthouseThemeDemo } from '@/components/lighthouse';

function App() {
  return <LighthouseThemeDemo />;
}
```

## 📋 Integration Options

### Option A: Full Theme Demo (Recommended for Showcasing)
```tsx
import { LighthouseThemeDemo } from '@/components/lighthouse';

export function App() {
  return <LighthouseThemeDemo />;
}
```
- **Features**: Welcome screen, theme selection, live preview
- **Best for**: Demos, showcasing capabilities, first-time users

### Option B: Quick Start (Recommended for Most Users)
```tsx
import { QuickStartLighthouse } from '@/components/lighthouse';

export function App() {
  return <QuickStartLighthouse />;
}
```
- **Features**: Immediate access with theme switcher button
- **Best for**: Regular usage, development, testing

### Option C: Custom Configuration
```tsx
import { LighthouseWithThemes } from '@/components/lighthouse';

export function App() {
  return (
    <LighthouseWithThemes
      defaultTheme="cyberpunk"
      showThemeSwitcher={true}
    />
  );
}
```
- **Features**: Custom default theme, optional theme switcher
- **Best for**: Specific requirements, branded applications

### Option D: Production Ready (Fixed Theme)
```tsx
import { ProductionLighthouse } from '@/components/lighthouse';

export function App() {
  return <ProductionLighthouse theme="glassmorphism" />;
}
```
- **Features**: Single theme, no switcher UI, optimized
- **Best for**: Production deployments, specific brand requirements

## 🎯 Available Themes

| Theme | Description | Best For |
|-------|-------------|----------|
| **Glassmorphism** | Modern frosted glass effects | Professional environments |
| **Neumorphism** | Soft, tactile button-like interfaces | Touch devices, accessibility |
| **Cyberpunk** | Futuristic neon glows on dark backgrounds | Gaming, developer tools |
| **Minimalist** | Clean, content-focused design | Research, academic use |
| **Brutalist** | Bold, high-contrast geometric design | Creative tools, art platforms |

## ⚙️ Configuration Options

### LighthouseWithThemes Props
```tsx
interface LighthouseWithThemesProps {
  defaultTheme?: DesignTheme;        // Default: 'glassmorphism'
  showThemeSwitcher?: boolean;       // Default: true
  className?: string;                // Additional CSS classes
}
```

### Theme Types
```tsx
type DesignTheme = 
  | 'glassmorphism'
  | 'neumorphism' 
  | 'cyberpunk'
  | 'minimalist'
  | 'brutalist';
```

## 💾 Theme Persistence

Themes are automatically saved to localStorage:
- **Key**: `lighthouse-preferred-theme`
- **Value**: Selected theme name
- **Behavior**: Restored on page reload

## 🎨 Theme Switcher Features

### User Interface
- **Floating button** in top-right corner
- **Color palette preview** on the button
- **Modal interface** for theme selection
- **Live previews** of each theme
- **Theme descriptions** and use cases

### User Experience
- **First-time hint** for new users
- **Smooth transitions** between themes
- **Accessibility support** (keyboard navigation, screen readers)
- **Reduced motion** respect for user preferences

## 🔧 Customization

### Adding Custom Themes
1. Create a new theme component in `design-mockups/`
2. Add theme configuration to `DESIGN_THEMES`
3. Export from `design-mockups/index.ts`

### Modifying Existing Themes
1. Edit the theme component file
2. Update color palettes in `DESIGN_THEMES`
3. Adjust theme characteristics and descriptions

### Custom Theme Switcher UI
1. Create your own theme switcher component
2. Use the `useThemeSwitcher` hook
3. Implement custom UI with theme selection logic

## 🎯 Migration from Existing Lighthouse

### Step 1: Update Imports
```tsx
// Old
import { LighthouseMain } from '@/components/lighthouse';

// New
import { LighthouseWithThemes } from '@/components/lighthouse';
```

### Step 2: Update Component Usage
```tsx
// Old
<LighthouseMain 
  initialModule="dashboard"
  className="custom-class"
/>

// New
<LighthouseWithThemes
  defaultTheme="glassmorphism"
  showThemeSwitcher={true}
  className="custom-class"
/>
```

### Step 3: Test All Themes
1. Run your application
2. Click the theme switcher button
3. Test each theme for functionality
4. Verify accessibility features work

## 🧪 Testing

### Manual Testing Checklist
- [ ] All themes load correctly
- [ ] Theme switcher button appears
- [ ] Theme selection modal opens/closes
- [ ] Theme preferences persist on reload
- [ ] Animations respect reduced motion
- [ ] Keyboard navigation works
- [ ] Screen reader compatibility

### Automated Testing
```tsx
import { render, screen } from '@testing-library/react';
import { LighthouseWithThemes } from '@/components/lighthouse';

test('theme switcher renders', () => {
  render(<LighthouseWithThemes showThemeSwitcher={true} />);
  expect(screen.getByLabelText('Open theme switcher')).toBeInTheDocument();
});
```

## 🚀 Performance

### Optimizations Included
- **Lazy loading** of theme components
- **GPU-accelerated** animations
- **Efficient re-rendering** with React patterns
- **Bundle splitting** by theme
- **Memory management** for unused themes

### Performance Tips
- Use `ProductionLighthouse` for fixed-theme deployments
- Disable theme switcher in production if not needed
- Monitor bundle size when adding custom themes

## 🔍 Troubleshooting

### Common Issues

**Theme switcher not appearing:**
- Check `showThemeSwitcher={true}` prop
- Verify component is properly imported
- Check for CSS conflicts

**Themes not switching:**
- Verify theme names are correct
- Check browser console for errors
- Ensure localStorage is available

**Animations not working:**
- Check user's motion preferences
- Verify Framer Motion is installed
- Test in different browsers

**Styling conflicts:**
- Check for CSS specificity issues
- Verify Tailwind classes are available
- Test with different base styles

## 📚 Examples

See `examples/ThemeIntegrationExamples.tsx` for complete working examples of all integration patterns.

## 🎉 What's Preserved

All your existing Lighthouse features remain intact:
- ✅ Component architecture and modularity
- ✅ State management with Zustand
- ✅ Accessibility features and WCAG compliance
- ✅ Responsive design and mobile support
- ✅ Animation preferences (prefers-reduced-motion)
- ✅ Error boundaries and loading states
- ✅ TypeScript type safety
- ✅ Performance optimizations

The theme system is purely additive - it enhances the visual presentation without changing any core functionality.
