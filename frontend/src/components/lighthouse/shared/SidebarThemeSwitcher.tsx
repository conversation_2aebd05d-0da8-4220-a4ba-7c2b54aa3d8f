import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '~/lib/ui-utils';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { 
  DESIGN_THEMES, 
  type DesignTheme,
  type ThemeConfig 
} from '../design-mockups/theme-config';
import { useThemeSwitcher } from '../design-mockups/theme-hooks';

// Temporary implementation for demo
const AccessibilityUtils = {
  prefersReducedMotion: () => {
    if (typeof window !== 'undefined') {
      return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    }
    return false;
  }
};

interface SidebarThemeSwitcherProps {
  className?: string;
  compact?: boolean;
}

export function SidebarThemeSwitcher({ className, compact = false }: SidebarThemeSwitcherProps) {
  const { currentTheme, switchTheme, availableThemes } = useThemeSwitcher();
  const [isExpanded, setIsExpanded] = useState(false);
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  const currentConfig = DESIGN_THEMES[currentTheme];

  const handleThemeChange = (theme: DesignTheme) => {
    switchTheme(theme);
    if (compact) {
      setIsExpanded(false);
    }
  };

  if (compact) {
    return (
      <div className={cn('relative', className)}>
        {/* Compact Theme Switcher Button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsExpanded(!isExpanded)}
          className={cn(
            'w-full justify-start gap-3 h-10',
            'hover:bg-accent/50 transition-all duration-200',
            'group relative overflow-hidden'
          )}
          aria-label="Change theme"
        >
          {/* Theme Color Indicator */}
          <div className="flex items-center gap-1">
            {currentConfig.colorPalette.slice(0, 3).map((color, index) => (
              <div
                key={index}
                className="w-2.5 h-2.5 rounded-full border border-border/30"
                style={{ backgroundColor: color }}
              />
            ))}
          </div>
          
          <span className="text-sm font-medium flex-1 text-left">
            {compact ? 'Theme' : currentConfig.name}
          </span>
          
          <motion.div
            animate={{ rotate: isExpanded ? 180 : 0 }}
            transition={{ duration: 0.2 }}
            className="text-muted-foreground"
          >
            ▼
          </motion.div>
        </Button>

        {/* Expanded Theme Options */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, y: -10 }}
              animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, y: 0 }}
              exit={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, y: -10 }}
              transition={{ duration: prefersReducedMotion ? 0.1 : 0.2 }}
              className="absolute left-0 right-0 top-full mt-1 z-50"
            >
              <Card className="shadow-lg border bg-background/95 backdrop-blur-sm">
                <CardContent className="p-2">
                  <div className="space-y-1">
                    {availableThemes.map((theme) => {
                      const config = DESIGN_THEMES[theme];
                      const isActive = currentTheme === theme;

                      return (
                        <button
                          key={theme}
                          onClick={() => handleThemeChange(theme)}
                          className={cn(
                            'w-full flex items-center gap-3 p-2 rounded-md transition-all duration-200',
                            'hover:bg-accent/50 text-left',
                            isActive && 'bg-accent text-accent-foreground'
                          )}
                        >
                          <div className="flex gap-1">
                            {config.colorPalette.slice(0, 3).map((color, index) => (
                              <div
                                key={index}
                                className="w-2.5 h-2.5 rounded-full border border-border/30"
                                style={{ backgroundColor: color }}
                              />
                            ))}
                          </div>
                          <span className="text-sm font-medium flex-1">{config.name}</span>
                          {isActive && (
                            <Badge variant="secondary" className="text-xs">
                              Active
                            </Badge>
                          )}
                        </button>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  }

  // Full sidebar theme switcher
  return (
    <div className={cn('space-y-3', className)}>
      {/* Current Theme Display */}
      <div className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
        <div className="flex gap-1">
          {currentConfig.colorPalette.slice(0, 4).map((color, index) => (
            <div
              key={index}
              className="w-3 h-3 rounded-full border border-border/30"
              style={{ backgroundColor: color }}
            />
          ))}
        </div>
        <div className="flex-1">
          <p className="text-sm font-medium">{currentConfig.name}</p>
          <p className="text-xs text-muted-foreground">{currentConfig.description}</p>
        </div>
      </div>

      {/* Theme Options */}
      <div className="space-y-2">
        <h4 className="text-sm font-medium text-muted-foreground px-1">Available Themes</h4>
        <div className="space-y-1">
          {availableThemes.map((theme, index) => {
            const config = DESIGN_THEMES[theme];
            const isActive = currentTheme === theme;

            return (
              <motion.button
                key={theme}
                initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, x: -10 }}
                animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, x: 0 }}
                transition={{ duration: prefersReducedMotion ? 0.1 : 0.2, delay: index * 0.05 }}
                onClick={() => handleThemeChange(theme)}
                className={cn(
                  'w-full flex items-center gap-3 p-2 rounded-md transition-all duration-200',
                  'hover:bg-accent/50 text-left group',
                  isActive && 'bg-accent text-accent-foreground shadow-sm'
                )}
              >
                {/* Theme Preview */}
                <div className="relative">
                  <div
                    className="w-8 h-6 rounded border border-border/30 overflow-hidden"
                    style={{
                      background: `linear-gradient(135deg, ${config.colorPalette[0]} 0%, ${config.colorPalette[1]} 100%)`
                    }}
                  />
                  {isActive && (
                    <div className="absolute inset-0 border-2 border-primary rounded" />
                  )}
                </div>

                <div className="flex-1">
                  <p className="text-sm font-medium">{config.name}</p>
                  <p className="text-xs text-muted-foreground">
                    {config.characteristics.slice(0, 2).join(', ')}
                  </p>
                </div>

                {isActive && (
                  <Badge variant="default" className="text-xs">
                    Active
                  </Badge>
                )}
              </motion.button>
            );
          })}
        </div>
      </div>

      {/* Theme Info */}
      <div className="p-3 bg-muted/20 rounded-lg">
        <h4 className="text-sm font-medium mb-2">Current Theme Features</h4>
        <div className="space-y-1">
          {currentConfig.characteristics.map((char, index) => (
            <div key={index} className="flex items-center gap-2 text-xs text-muted-foreground">
              <div className="w-1 h-1 bg-primary rounded-full" />
              <span>{char}</span>
            </div>
          ))}
        </div>
        <div className="mt-2 pt-2 border-t border-border/30">
          <p className="text-xs text-muted-foreground">
            Best for: {currentConfig.bestFor.slice(0, 2).join(', ')}
          </p>
        </div>
      </div>
    </div>
  );
}

export default SidebarThemeSwitcher;
