import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '~/lib/ui-utils';

// Brutalist Bold Design Mockup
export function BrutalistBoldDashboard() {
  return (
    <div className="min-h-screen bg-yellow-50 dark:bg-gray-900 font-mono">
      {/* Header */}
      <header className="bg-black text-white border-b-8 border-red-500">
        <div className="max-w-7xl mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              <div className="w-16 h-16 bg-red-500 border-4 border-black flex items-center justify-center transform rotate-3">
                <span className="text-black font-black text-2xl transform -rotate-3">L</span>
              </div>
              <div>
                <h1 className="text-4xl font-black tracking-tight">LIGHTHOUSE</h1>
                <p className="text-xl font-bold text-yellow-400 tracking-wide">AI RESEARCH MACHINE</p>
              </div>
            </div>
            <div className="flex items-center gap-6">
              <div className="px-6 py-3 bg-green-500 border-4 border-black text-black font-black text-lg transform -rotate-1">
                SYSTEM ACTIVE
              </div>
              <button className="px-8 py-4 bg-yellow-400 border-4 border-black text-black font-black text-lg hover:bg-yellow-300 transform hover:rotate-1 transition-all duration-200">
                NEW PROJECT
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-6 py-8">
        {/* Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {[
            { title: "NEURAL LOAD", value: "87%", color: "bg-red-500", border: "border-red-700" },
            { title: "DATA NODES", value: "2,847", color: "bg-blue-500", border: "border-blue-700" },
            { title: "ACTIVE PROCS", value: "07", color: "bg-green-500", border: "border-green-700" },
            { title: "INSIGHTS", value: "23", color: "bg-purple-500", border: "border-purple-700" }
          ].map((metric, index) => (
            <motion.div
              key={metric.title}
              initial={{ opacity: 0, y: 20, rotate: 0 }}
              animate={{ opacity: 1, y: 0, rotate: index % 2 === 0 ? 2 : -2 }}
              transition={{ delay: index * 0.1, duration: 0.5 }}
              className="group"
            >
              <div className={cn(
                "p-8 border-8 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] hover:shadow-[12px_12px_0px_0px_rgba(0,0,0,1)] transition-all duration-200 hover:transform hover:scale-105",
                metric.color
              )}>
                <div className="text-center">
                  <div className="text-6xl font-black text-black mb-4">{metric.value}</div>
                  <div className="text-xl font-black text-black tracking-wider">{metric.title}</div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Main Dashboard Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Central Processing */}
          <div className="lg:col-span-2">
            <div className="bg-white border-8 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] p-8">
              <div className="flex items-center gap-4 mb-8">
                <div className="w-8 h-8 bg-red-500 border-4 border-black transform rotate-45" />
                <div>
                  <h2 className="text-3xl font-black text-black">NEURAL MATRIX</h2>
                  <p className="text-lg font-bold text-gray-700">REAL-TIME PROCESSING</p>
                </div>
              </div>

              {/* Matrix Visualization */}
              <div className="relative h-96 bg-black border-4 border-gray-800 mb-8 overflow-hidden">
                <div className="absolute inset-4">
                  {/* Brutalist Nodes */}
                  {[
                    { x: '20%', y: '30%', size: 'w-8 h-8', color: 'bg-red-500', rotation: 'rotate-12' },
                    { x: '60%', y: '20%', size: 'w-6 h-6', color: 'bg-blue-500', rotation: 'rotate-45' },
                    { x: '40%', y: '60%', size: 'w-10 h-10', color: 'bg-green-500', rotation: '-rotate-12' },
                    { x: '80%', y: '50%', size: 'w-7 h-7', color: 'bg-yellow-500', rotation: 'rotate-90' },
                    { x: '15%', y: '70%', size: 'w-5 h-5', color: 'bg-purple-500', rotation: '-rotate-45' }
                  ].map((node, index) => (
                    <motion.div
                      key={index}
                      className={cn(
                        "absolute border-4 border-white",
                        node.size,
                        node.color,
                        node.rotation
                      )}
                      style={{ left: node.x, top: node.y }}
                      animate={{
                        scale: [1, 1.3, 1],
                        rotate: [0, 360, 0]
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        delay: index * 0.5
                      }}
                    />
                  ))}

                  {/* Connection Lines */}
                  <svg className="absolute inset-0 w-full h-full">
                    <defs>
                      <pattern id="brutalistPattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
                        <rect width="20" height="20" fill="none" stroke="#ffffff" strokeWidth="2" strokeDasharray="5,5" />
                      </pattern>
                    </defs>
                    <g stroke="#ffffff" strokeWidth="4" fill="none">
                      <path d="M 20% 30% L 60% 20%" strokeDasharray="10,5" />
                      <path d="M 60% 20% L 40% 60%" strokeDasharray="10,5" />
                      <path d="M 40% 60% L 80% 50%" strokeDasharray="10,5" />
                      <path d="M 15% 70% L 40% 60%" strokeDasharray="10,5" />
                    </g>
                  </svg>
                </div>

                <div className="absolute bottom-4 left-4">
                  <div className="px-4 py-2 bg-yellow-400 border-4 border-black text-black font-black">
                    DATA FLOW: MAXIMUM
                  </div>
                </div>
              </div>

              {/* Process Monitor */}
              <div className="space-y-4">
                <h3 className="text-2xl font-black text-black">PROCESS MONITOR</h3>
                {[
                  { process: "NEURAL_ANALYSIS.EXE", status: "RUNNING", progress: 78, color: "bg-red-500" },
                  { process: "KNOWLEDGE_BUILD.EXE", status: "RUNNING", progress: 45, color: "bg-blue-500" },
                  { process: "INSIGHT_GEN.EXE", status: "CRITICAL", progress: 92, color: "bg-green-500" }
                ].map((proc, index) => (
                  <div key={index} className="bg-gray-100 border-4 border-black p-4">
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-lg font-black text-black">{proc.process}</span>
                      <div className={cn(
                        "px-3 py-1 border-2 border-black text-black font-black text-sm",
                        proc.status === 'CRITICAL' ? 'bg-red-400' : 'bg-green-400'
                      )}>
                        {proc.status}
                      </div>
                    </div>
                    <div className="w-full h-6 bg-white border-4 border-black overflow-hidden">
                      <motion.div
                        className={cn("h-full border-r-4 border-black", proc.color)}
                        initial={{ width: 0 }}
                        animate={{ width: `${proc.progress}%` }}
                        transition={{ duration: 1, delay: index * 0.2 }}
                      />
                    </div>
                    <div className="text-right mt-1">
                      <span className="text-lg font-black text-black">{proc.progress}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Control Panel */}
          <div className="space-y-8">
            {/* System Status */}
            <div className="bg-white border-8 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] p-6">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-6 h-6 bg-green-500 border-4 border-black transform rotate-45" />
                <h3 className="text-2xl font-black text-black">STATUS</h3>
              </div>
              
              <div className="space-y-4">
                {[
                  { label: "NEURAL PROC", status: "OPTIMAL", color: "bg-green-400" },
                  { label: "MEMORY USE", status: "NORMAL", color: "bg-blue-400" },
                  { label: "LEARN RATE", status: "HIGH", color: "bg-red-400" }
                ].map((item, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-lg font-black text-black">{item.label}</span>
                    <div className={cn(
                      "px-3 py-1 border-4 border-black text-black font-black text-sm transform rotate-1",
                      item.color
                    )}>
                      {item.status}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Command Center */}
            <div className="bg-white border-8 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] p-6">
              <h3 className="text-2xl font-black text-black mb-6">COMMANDS</h3>
              <div className="grid grid-cols-2 gap-4">
                {[
                  { cmd: "BOOST", color: "bg-yellow-400", icon: "⚡" },
                  { cmd: "SCAN", color: "bg-blue-400", icon: "🔍" },
                  { cmd: "LEARN", color: "bg-green-400", icon: "🧠" },
                  { cmd: "SYNC", color: "bg-red-400", icon: "🔄" }
                ].map((command, index) => (
                  <motion.button
                    key={command.cmd}
                    whileHover={{ scale: 1.05, rotate: 2 }}
                    whileTap={{ scale: 0.95, rotate: -2 }}
                    className={cn(
                      "p-4 border-4 border-black text-black font-black text-lg shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transition-all duration-200",
                      command.color
                    )}
                  >
                    <div className="text-2xl mb-1">{command.icon}</div>
                    <div>{command.cmd}</div>
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Alert System */}
            <div className="bg-red-500 border-8 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] p-6">
              <div className="flex items-center gap-3 mb-4">
                <motion.div
                  className="w-6 h-6 bg-yellow-400 border-4 border-black"
                  animate={{ rotate: [0, 90, 180, 270, 360] }}
                  transition={{ duration: 2, repeat: Infinity }}
                />
                <h3 className="text-2xl font-black text-black">ALERTS</h3>
              </div>
              
              <div className="space-y-3">
                {[
                  "HIGH CPU USAGE DETECTED",
                  "NEW INSIGHTS AVAILABLE",
                  "SYSTEM OPTIMIZATION READY"
                ].map((alert, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.3 }}
                    className="bg-yellow-400 border-4 border-black p-3 text-black font-black text-sm"
                  >
                    {alert}
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
