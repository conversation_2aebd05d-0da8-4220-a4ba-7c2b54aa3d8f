import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '~/lib/ui-utils';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';

// Minimalist Clean Design Mockup
export function MinimalistCleanDashboard() {
  return (
    <div className="min-h-screen bg-white dark:bg-gray-950">
      {/* Header */}
      <header className="border-b border-gray-100 dark:border-gray-800 bg-white/80 dark:bg-gray-950/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="max-w-6xl mx-auto px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              <div className="w-8 h-8 bg-black dark:bg-white rounded-full flex items-center justify-center">
                <div className="w-3 h-3 bg-white dark:bg-black rounded-full" />
              </div>
              <div>
                <h1 className="text-2xl font-light text-gray-900 dark:text-gray-100">Lighthouse</h1>
                <p className="text-sm text-gray-500 dark:text-gray-400 font-light">Intelligence Platform</p>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full" />
                <span className="text-sm text-gray-600 dark:text-gray-400 font-light">Online</span>
              </div>
              <Button variant="outline" className="font-light border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-900">
                New Project
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-8 py-12">
        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16">
          {[
            { label: "Learning Progress", value: "94", unit: "%" },
            { label: "Knowledge Nodes", value: "2,847", unit: "" },
            { label: "Active Processes", value: "7", unit: "" },
            { label: "Daily Insights", value: "23", unit: "" }
          ].map((metric, index) => (
            <motion.div
              key={metric.label}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.6, ease: "easeOut" }}
              className="group"
            >
              <div className="text-center">
                <div className="mb-4">
                  <span className="text-4xl font-extralight text-gray-900 dark:text-gray-100">
                    {metric.value}
                  </span>
                  <span className="text-2xl font-extralight text-gray-400 dark:text-gray-500">
                    {metric.unit}
                  </span>
                </div>
                <p className="text-sm font-light text-gray-600 dark:text-gray-400 tracking-wide">
                  {metric.label}
                </p>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Main Dashboard */}
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-12">
          {/* Primary Content */}
          <div className="lg:col-span-3 space-y-12">
            {/* Intelligence Overview */}
            <section>
              <div className="mb-8">
                <h2 className="text-xl font-light text-gray-900 dark:text-gray-100 mb-2">Intelligence Overview</h2>
                <p className="text-sm text-gray-500 dark:text-gray-400 font-light">Real-time cognitive processing and learning</p>
              </div>

              <div className="relative">
                {/* Knowledge Graph Visualization */}
                <div className="h-80 bg-gray-50 dark:bg-gray-900 rounded-2xl relative overflow-hidden">
                  <div className="absolute inset-8">
                    {/* Minimalist Nodes */}
                    {[
                      { x: '25%', y: '30%', size: 'w-3 h-3' },
                      { x: '45%', y: '20%', size: 'w-4 h-4' },
                      { x: '65%', y: '40%', size: 'w-5 h-5' },
                      { x: '35%', y: '60%', size: 'w-3 h-3' },
                      { x: '75%', y: '65%', size: 'w-4 h-4' },
                      { x: '15%', y: '70%', size: 'w-2 h-2' }
                    ].map((node, index) => (
                      <motion.div
                        key={index}
                        className={cn("absolute rounded-full bg-gray-900 dark:bg-gray-100", node.size)}
                        style={{ left: node.x, top: node.y }}
                        animate={{
                          scale: [1, 1.1, 1],
                          opacity: [0.6, 1, 0.6]
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          delay: index * 0.5
                        }}
                      />
                    ))}

                    {/* Minimalist Connections */}
                    <svg className="absolute inset-0 w-full h-full">
                      <defs>
                        <linearGradient id="connectionLine" x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" stopColor="currentColor" stopOpacity="0.2" />
                          <stop offset="50%" stopColor="currentColor" stopOpacity="0.4" />
                          <stop offset="100%" stopColor="currentColor" stopOpacity="0.2" />
                        </linearGradient>
                      </defs>
                      <g className="text-gray-400 dark:text-gray-600">
                        <path d="M 25% 30% L 45% 20%" stroke="url(#connectionLine)" strokeWidth="1" />
                        <path d="M 45% 20% L 65% 40%" stroke="url(#connectionLine)" strokeWidth="1" />
                        <path d="M 65% 40% L 75% 65%" stroke="url(#connectionLine)" strokeWidth="1" />
                        <path d="M 35% 60% L 65% 40%" stroke="url(#connectionLine)" strokeWidth="1" />
                        <path d="M 25% 30% L 35% 60%" stroke="url(#connectionLine)" strokeWidth="1" />
                      </g>
                    </svg>
                  </div>

                  <div className="absolute bottom-6 left-6">
                    <p className="text-sm font-light text-gray-600 dark:text-gray-400">Knowledge Network</p>
                    <p className="text-xs text-gray-400 dark:text-gray-500 font-light">156 concepts connected</p>
                  </div>
                </div>
              </div>
            </section>

            {/* Recent Activity */}
            <section>
              <div className="mb-8">
                <h2 className="text-xl font-light text-gray-900 dark:text-gray-100 mb-2">Recent Activity</h2>
                <p className="text-sm text-gray-500 dark:text-gray-400 font-light">Latest system events and insights</p>
              </div>

              <div className="space-y-6">
                {[
                  {
                    time: "2 minutes ago",
                    event: "Generated new insight on machine learning patterns",
                    type: "insight"
                  },
                  {
                    time: "15 minutes ago",
                    event: "Updated knowledge graph with 12 new connections",
                    type: "update"
                  },
                  {
                    time: "1 hour ago",
                    event: "Completed analysis of research documents",
                    type: "completion"
                  },
                  {
                    time: "3 hours ago",
                    event: "Started learning from new data sources",
                    type: "learning"
                  }
                ].map((activity, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.5 }}
                    className="flex items-start gap-4 pb-6 border-b border-gray-100 dark:border-gray-800 last:border-b-0"
                  >
                    <div className="w-2 h-2 bg-gray-300 dark:bg-gray-600 rounded-full mt-2 flex-shrink-0" />
                    <div className="flex-1">
                      <p className="text-sm text-gray-900 dark:text-gray-100 font-light leading-relaxed">
                        {activity.event}
                      </p>
                      <p className="text-xs text-gray-400 dark:text-gray-500 font-light mt-1">
                        {activity.time}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </section>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-2 space-y-12">
            {/* System Status */}
            <section>
              <div className="mb-6">
                <h3 className="text-lg font-light text-gray-900 dark:text-gray-100 mb-2">System Status</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 font-light">Current operational state</p>
              </div>

              <div className="space-y-4">
                {[
                  { label: "Neural Processing", status: "Optimal", value: 94 },
                  { label: "Memory Usage", status: "Normal", value: 67 },
                  { label: "Learning Rate", status: "High", value: 89 }
                ].map((item, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-light text-gray-600 dark:text-gray-400">{item.label}</span>
                      <span className="text-sm font-light text-gray-900 dark:text-gray-100">{item.status}</span>
                    </div>
                    <div className="w-full h-px bg-gray-200 dark:bg-gray-700 overflow-hidden">
                      <motion.div
                        className="h-full bg-gray-900 dark:bg-gray-100"
                        initial={{ width: 0 }}
                        animate={{ width: `${item.value}%` }}
                        transition={{ duration: 1, delay: index * 0.2 }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* Quick Actions */}
            <section>
              <div className="mb-6">
                <h3 className="text-lg font-light text-gray-900 dark:text-gray-100 mb-2">Quick Actions</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 font-light">Common tasks and operations</p>
              </div>

              <div className="space-y-3">
                {[
                  "Add Knowledge Source",
                  "Start Research Session",
                  "Deploy Learning Agent",
                  "Generate Insights",
                  "Export Knowledge Graph"
                ].map((action, index) => (
                  <motion.button
                    key={action}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.4 }}
                    className="w-full text-left p-4 text-sm font-light text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-900 rounded-lg transition-all duration-200"
                  >
                    {action}
                  </motion.button>
                ))}
              </div>
            </section>

            {/* Current Focus */}
            <section>
              <div className="mb-6">
                <h3 className="text-lg font-light text-gray-900 dark:text-gray-100 mb-2">Current Focus</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 font-light">Active research areas</p>
              </div>

              <div className="space-y-4">
                {[
                  { area: "Machine Learning", progress: 87 },
                  { area: "Neural Networks", progress: 72 },
                  { area: "Data Science", progress: 94 }
                ].map((focus, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-light text-gray-900 dark:text-gray-100">{focus.area}</span>
                      <span className="text-xs font-light text-gray-400 dark:text-gray-500">{focus.progress}%</span>
                    </div>
                    <div className="w-full h-px bg-gray-200 dark:bg-gray-700">
                      <motion.div
                        className="h-full bg-gray-400 dark:bg-gray-500"
                        initial={{ width: 0 }}
                        animate={{ width: `${focus.progress}%` }}
                        transition={{ duration: 1, delay: index * 0.3 }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </section>
          </div>
        </div>
      </main>
    </div>
  );
}
