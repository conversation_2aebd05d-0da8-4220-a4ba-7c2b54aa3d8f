import { useState, useCallback } from 'react';
import { 
  type DesignTheme, 
  getThemeComponent, 
  getThemeConfig, 
  getAllThemes 
} from './theme-config';

// Theme Switcher Hook
export const useThemeSwitcher = (defaultTheme: DesignTheme = 'glassmorphism') => {
  const [currentTheme, setCurrentTheme] = useState<DesignTheme>(defaultTheme);
  
  const switchTheme = useCallback((theme: DesignTheme) => {
    setCurrentTheme(theme);
  }, []);
  
  const getCurrentComponent = useCallback(() => {
    return getThemeComponent(currentTheme);
  }, [currentTheme]);
  
  const getCurrentConfig = useCallback(() => {
    return getThemeConfig(currentTheme);
  }, [currentTheme]);
  
  return {
    currentTheme,
    switchTheme,
    getCurrentComponent,
    getCurrentConfig,
    availableThemes: getAllThemes()
  };
};
