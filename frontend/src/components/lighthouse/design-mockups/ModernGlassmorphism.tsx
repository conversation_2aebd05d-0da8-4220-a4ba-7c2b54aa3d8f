import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '~/lib/ui-utils';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';

// Modern Glassmorphism Design Mockup
export function ModernGlassmorphismDashboard() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Floating Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-cyan-400/20 to-blue-400/20 rounded-full blur-3xl animate-pulse delay-1000" />
      </div>

      {/* Header with Glassmorphism */}
      <header className="relative z-10 backdrop-blur-xl bg-white/30 dark:bg-slate-900/30 border-b border-white/20 dark:border-slate-700/30">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-lg">L</span>
              </div>
              <div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
                  Lighthouse AI
                </h1>
                <p className="text-sm text-slate-600 dark:text-slate-400">Intelligent Research Platform</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Badge variant="outline" className="backdrop-blur-sm bg-green-500/10 border-green-500/30 text-green-700 dark:text-green-400">
                AI Active
              </Badge>
              <Button className="backdrop-blur-sm bg-blue-500/90 hover:bg-blue-600/90 text-white border-0">
                New Project
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 max-w-7xl mx-auto px-6 py-8">
        {/* Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {[
            { title: "Learning Progress", value: "87%", change: "+12%", color: "from-blue-500 to-cyan-500" },
            { title: "Knowledge Depth", value: "156", change: "+23", color: "from-purple-500 to-pink-500" },
            { title: "Active Agents", value: "3", change: "+1", color: "from-green-500 to-emerald-500" },
            { title: "Insights Generated", value: "42", change: "+8", color: "from-orange-500 to-red-500" }
          ].map((metric, index) => (
            <motion.div
              key={metric.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="backdrop-blur-xl bg-white/40 dark:bg-slate-800/40 border-white/20 dark:border-slate-700/30 hover:bg-white/50 dark:hover:bg-slate-800/50 transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className={cn("w-12 h-12 rounded-xl bg-gradient-to-r", metric.color, "flex items-center justify-center")}>
                      <div className="w-6 h-6 bg-white/30 rounded-lg" />
                    </div>
                    <Badge variant="secondary" className="bg-white/50 dark:bg-slate-700/50 text-green-600 dark:text-green-400">
                      {metric.change}
                    </Badge>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-slate-900 dark:text-white">{metric.value}</p>
                    <p className="text-sm text-slate-600 dark:text-slate-400">{metric.title}</p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Project Overview */}
          <div className="lg:col-span-2">
            <Card className="backdrop-blur-xl bg-white/40 dark:bg-slate-800/40 border-white/20 dark:border-slate-700/30 h-full">
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <div className="w-4 h-4 bg-white/30 rounded" />
                  </div>
                  Project Intelligence
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Knowledge Graph Visualization */}
                <div className="relative h-64 bg-gradient-to-br from-blue-50/50 to-purple-50/50 dark:from-slate-700/30 dark:to-slate-600/30 rounded-xl overflow-hidden">
                  <div className="absolute inset-4">
                    {/* Simulated Knowledge Nodes */}
                    {[
                      { x: '20%', y: '30%', size: 'w-8 h-8', color: 'bg-blue-500' },
                      { x: '60%', y: '20%', size: 'w-6 h-6', color: 'bg-purple-500' },
                      { x: '40%', y: '60%', size: 'w-10 h-10', color: 'bg-cyan-500' },
                      { x: '80%', y: '50%', size: 'w-7 h-7', color: 'bg-green-500' },
                      { x: '15%', y: '70%', size: 'w-5 h-5', color: 'bg-pink-500' }
                    ].map((node, index) => (
                      <motion.div
                        key={index}
                        className={cn("absolute rounded-full", node.size, node.color, "opacity-80")}
                        style={{ left: node.x, top: node.y }}
                        animate={{
                          scale: [1, 1.2, 1],
                          opacity: [0.8, 1, 0.8]
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          delay: index * 0.3
                        }}
                      />
                    ))}
                    {/* Connection Lines */}
                    <svg className="absolute inset-0 w-full h-full">
                      <defs>
                        <linearGradient id="connectionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" stopColor="rgb(59, 130, 246)" stopOpacity="0.3" />
                          <stop offset="100%" stopColor="rgb(147, 51, 234)" stopOpacity="0.3" />
                        </linearGradient>
                      </defs>
                      <path d="M 20% 30% Q 40% 10% 60% 20%" stroke="url(#connectionGradient)" strokeWidth="2" fill="none" />
                      <path d="M 60% 20% Q 50% 40% 40% 60%" stroke="url(#connectionGradient)" strokeWidth="2" fill="none" />
                      <path d="M 40% 60% Q 60% 55% 80% 50%" stroke="url(#connectionGradient)" strokeWidth="2" fill="none" />
                    </svg>
                  </div>
                  <div className="absolute bottom-4 left-4">
                    <p className="text-sm font-medium text-slate-700 dark:text-slate-300">Knowledge Graph</p>
                    <p className="text-xs text-slate-500 dark:text-slate-400">156 concepts • 89 connections</p>
                  </div>
                </div>

                {/* Recent Activity */}
                <div className="space-y-3">
                  <h4 className="font-semibold text-slate-900 dark:text-white">Recent Activity</h4>
                  {[
                    { action: "New insight generated", time: "2 min ago", type: "insight" },
                    { action: "Knowledge graph updated", time: "15 min ago", type: "update" },
                    { action: "Agent completed research", time: "1 hour ago", type: "completion" }
                  ].map((activity, index) => (
                    <div key={index} className="flex items-center gap-3 p-3 rounded-lg bg-white/30 dark:bg-slate-700/30">
                      <div className={cn(
                        "w-2 h-2 rounded-full",
                        activity.type === 'insight' ? 'bg-yellow-500' :
                        activity.type === 'update' ? 'bg-blue-500' : 'bg-green-500'
                      )} />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-slate-900 dark:text-white">{activity.action}</p>
                        <p className="text-xs text-slate-500 dark:text-slate-400">{activity.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* AI Assistant */}
            <Card className="backdrop-blur-xl bg-white/40 dark:bg-slate-800/40 border-white/20 dark:border-slate-700/30">
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                    <div className="w-4 h-4 bg-white/30 rounded-full animate-pulse" />
                  </div>
                  AI Assistant
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-4 bg-gradient-to-r from-blue-50/50 to-cyan-50/50 dark:from-slate-700/30 dark:to-slate-600/30 rounded-lg">
                  <p className="text-sm text-slate-700 dark:text-slate-300">
                    "I've identified 3 new research opportunities based on your recent knowledge additions. Would you like me to create research agents for these areas?"
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button size="sm" className="flex-1 bg-blue-500/90 hover:bg-blue-600/90 text-white">
                    Yes, Create
                  </Button>
                  <Button size="sm" variant="outline" className="backdrop-blur-sm bg-white/50 dark:bg-slate-700/50">
                    Later
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="backdrop-blur-xl bg-white/40 dark:bg-slate-800/40 border-white/20 dark:border-slate-700/30">
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {[
                  { label: "Add Knowledge", icon: "📚", color: "from-blue-500 to-cyan-500" },
                  { label: "Start Research", icon: "🔬", color: "from-purple-500 to-pink-500" },
                  { label: "Deploy Agent", icon: "🤖", color: "from-green-500 to-emerald-500" },
                  { label: "View Insights", icon: "💡", color: "from-yellow-500 to-orange-500" }
                ].map((action, index) => (
                  <Button
                    key={action.label}
                    variant="ghost"
                    className="w-full justify-start gap-3 h-12 backdrop-blur-sm bg-white/30 dark:bg-slate-700/30 hover:bg-white/50 dark:hover:bg-slate-700/50"
                  >
                    <div className={cn("w-8 h-8 rounded-lg bg-gradient-to-r", action.color, "flex items-center justify-center text-white")}>
                      {action.icon}
                    </div>
                    {action.label}
                  </Button>
                ))}
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}
