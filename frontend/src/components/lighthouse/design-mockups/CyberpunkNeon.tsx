import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '~/lib/ui-utils';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';

// Cyberpunk Neon Design Mockup
export function CyberpunkNeonDashboard() {
  return (
    <div className="min-h-screen bg-black relative overflow-hidden">
      {/* Animated Background Grid */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }} />
      </div>

      {/* Neon Glow Effects */}
      <div className="absolute top-0 left-1/4 w-96 h-96 bg-cyan-500/20 rounded-full blur-3xl animate-pulse" />
      <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-purple-500/20 rounded-full blur-3xl animate-pulse delay-1000" />
      <div className="absolute top-1/2 left-0 w-64 h-64 bg-pink-500/20 rounded-full blur-3xl animate-pulse delay-2000" />

      {/* Header */}
      <header className="relative z-10 border-b border-cyan-500/30 bg-black/80 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-lg flex items-center justify-center">
                  <span className="text-black font-bold text-xl">L</span>
                </div>
                <div className="absolute inset-0 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-lg blur-md opacity-50 -z-10" />
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
                  LIGHTHOUSE.AI
                </h1>
                <p className="text-cyan-300/80 text-sm font-mono">NEURAL_INTERFACE_v2.1</p>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="px-4 py-2 border border-green-500/50 rounded-lg bg-green-500/10 backdrop-blur-sm">
                <span className="text-green-400 text-sm font-mono">● SYSTEM_ONLINE</span>
              </div>
              <button className="px-6 py-2 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-lg text-black font-semibold hover:shadow-lg hover:shadow-cyan-500/50 transition-all duration-300">
                INIT_PROJECT
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 max-w-7xl mx-auto px-6 py-8">
        {/* System Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {[
            { title: "NEURAL_LOAD", value: "87.3%", unit: "PROC", color: "cyan", glow: "cyan-500" },
            { title: "DATA_NODES", value: "2,847", unit: "CONN", color: "purple", glow: "purple-500" },
            { title: "ACTIVE_PROCS", value: "07", unit: "EXEC", color: "green", glow: "green-500" },
            { title: "INSIGHTS_GEN", value: "23", unit: "NEW", color: "pink", glow: "pink-500" }
          ].map((metric, index) => (
            <motion.div
              key={metric.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="group"
            >
              <div className={cn(
                "relative p-6 bg-black/60 border rounded-lg backdrop-blur-sm transition-all duration-300 hover:scale-105",
                `border-${metric.color}-500/30 hover:border-${metric.color}-500/60`
              )}>
                <div className={cn(
                  "absolute inset-0 rounded-lg opacity-0 group-hover:opacity-20 transition-opacity duration-300",
                  `bg-${metric.color}-500 blur-xl`
                )} />
                <div className="relative z-10">
                  <div className="flex items-center justify-between mb-4">
                    <div className={cn(
                      "w-3 h-3 rounded-full animate-pulse",
                      `bg-${metric.color}-500 shadow-lg shadow-${metric.glow}/50`
                    )} />
                    <span className={cn(
                      "text-xs font-mono",
                      `text-${metric.color}-400`
                    )}>{metric.unit}</span>
                  </div>
                  <div>
                    <p className={cn(
                      "text-3xl font-bold font-mono mb-1",
                      `text-${metric.color}-400`
                    )}>{metric.value}</p>
                    <p className={cn(
                      "text-sm font-mono",
                      `text-${metric.color}-300/80`
                    )}>{metric.title}</p>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Main Interface Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Central Processing Unit */}
          <div className="lg:col-span-2">
            <div className="p-6 bg-black/60 border border-cyan-500/30 rounded-lg backdrop-blur-sm">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-4 h-4 bg-cyan-500 rounded-full animate-pulse shadow-lg shadow-cyan-500/50" />
                <div>
                  <h2 className="text-xl font-bold text-cyan-400 font-mono">NEURAL_MATRIX</h2>
                  <p className="text-cyan-300/60 text-sm font-mono">real_time_processing.exe</p>
                </div>
              </div>

              {/* Matrix Visualization */}
              <div className="relative h-80 bg-black/80 border border-cyan-500/20 rounded-lg mb-6 overflow-hidden">
                <div className="absolute inset-4">
                  {/* Matrix Rain Effect */}
                  {Array.from({ length: 20 }).map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute w-px bg-gradient-to-b from-transparent via-cyan-500 to-transparent"
                      style={{
                        left: `${(i * 5)}%`,
                        height: '100%'
                      }}
                      animate={{
                        opacity: [0, 1, 0],
                        y: ['-100%', '100%']
                      }}
                      transition={{
                        duration: 2 + Math.random() * 2,
                        repeat: Infinity,
                        delay: Math.random() * 2,
                        ease: 'linear'
                      }}
                    />
                  ))}

                  {/* Data Nodes */}
                  {[
                    { x: '20%', y: '30%', size: 'w-4 h-4', color: 'bg-cyan-500' },
                    { x: '60%', y: '20%', size: 'w-3 h-3', color: 'bg-purple-500' },
                    { x: '40%', y: '60%', size: 'w-5 h-5', color: 'bg-green-500' },
                    { x: '80%', y: '50%', size: 'w-4 h-4', color: 'bg-pink-500' },
                    { x: '15%', y: '70%', size: 'w-3 h-3', color: 'bg-yellow-500' }
                  ].map((node, index) => (
                    <motion.div
                      key={index}
                      className={cn("absolute rounded-full", node.size, node.color)}
                      style={{ left: node.x, top: node.y }}
                      animate={{
                        scale: [1, 1.5, 1],
                        opacity: [0.7, 1, 0.7],
                        boxShadow: [
                          `0 0 10px ${node.color.replace('bg-', '').replace('-500', '')}`,
                          `0 0 20px ${node.color.replace('bg-', '').replace('-500', '')}`,
                          `0 0 10px ${node.color.replace('bg-', '').replace('-500', '')}`
                        ]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        delay: index * 0.4
                      }}
                    />
                  ))}

                  {/* Connection Lines */}
                  <svg className="absolute inset-0 w-full h-full">
                    <defs>
                      <linearGradient id="dataFlow" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="#00ffff" stopOpacity="0.8" />
                        <stop offset="50%" stopColor="#8b5cf6" stopOpacity="0.6" />
                        <stop offset="100%" stopColor="#00ffff" stopOpacity="0.8" />
                      </linearGradient>
                    </defs>
                    <path d="M 20% 30% Q 40% 10% 60% 20%" stroke="url(#dataFlow)" strokeWidth="2" fill="none" strokeDasharray="5,5">
                      <animate attributeName="stroke-dashoffset" values="0;10" dur="1s" repeatCount="indefinite" />
                    </path>
                    <path d="M 60% 20% Q 50% 40% 40% 60%" stroke="url(#dataFlow)" strokeWidth="2" fill="none" strokeDasharray="5,5">
                      <animate attributeName="stroke-dashoffset" values="0;10" dur="1s" repeatCount="indefinite" begin="0.3s" />
                    </path>
                    <path d="M 40% 60% Q 60% 55% 80% 50%" stroke="url(#dataFlow)" strokeWidth="2" fill="none" strokeDasharray="5,5">
                      <animate attributeName="stroke-dashoffset" values="0;10" dur="1s" repeatCount="indefinite" begin="0.6s" />
                    </path>
                  </svg>
                </div>

                <div className="absolute bottom-4 left-4">
                  <div className="px-3 py-2 bg-black/80 border border-cyan-500/30 rounded backdrop-blur-sm">
                    <p className="text-cyan-400 text-sm font-mono">DATA_FLOW: ACTIVE</p>
                    <p className="text-cyan-300/60 text-xs font-mono">847 packets/sec</p>
                  </div>
                </div>
              </div>

              {/* Process Monitor */}
              <div className="space-y-3">
                <h3 className="text-lg font-bold text-cyan-400 font-mono">PROC_MONITOR</h3>
                {[
                  { process: "neural_pattern_analysis.exe", cpu: 78, status: "RUNNING", color: "cyan" },
                  { process: "knowledge_graph_builder.exe", cpu: 45, status: "RUNNING", color: "purple" },
                  { process: "insight_generator.exe", cpu: 92, status: "CRITICAL", color: "green" }
                ].map((proc, index) => (
                  <div key={index} className="p-3 bg-black/40 border border-gray-700/50 rounded font-mono text-sm">
                    <div className="flex items-center justify-between mb-2">
                      <span className={cn(
                        "font-medium",
                        proc.color === 'cyan' ? 'text-cyan-400' :
                        proc.color === 'purple' ? 'text-purple-400' : 'text-green-400'
                      )}>{proc.process}</span>
                      <div className="flex items-center gap-2">
                        <span className="text-gray-400">CPU: {proc.cpu}%</span>
                        <span className={cn(
                          "px-2 py-1 rounded text-xs",
                          proc.status === 'CRITICAL' ? 'bg-red-500/20 text-red-400 border border-red-500/30' :
                          'bg-green-500/20 text-green-400 border border-green-500/30'
                        )}>{proc.status}</span>
                      </div>
                    </div>
                    <div className="w-full h-1 bg-gray-800 rounded overflow-hidden">
                      <motion.div
                        className={cn(
                          "h-full rounded",
                          proc.color === 'cyan' ? 'bg-cyan-500' :
                          proc.color === 'purple' ? 'bg-purple-500' : 'bg-green-500'
                        )}
                        initial={{ width: 0 }}
                        animate={{ width: `${proc.cpu}%` }}
                        transition={{ duration: 1, delay: index * 0.2 }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Control Terminal */}
          <div className="space-y-6">
            {/* System Terminal */}
            <div className="p-6 bg-black/60 border border-green-500/30 rounded-lg backdrop-blur-sm">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse shadow-lg shadow-green-500/50" />
                <h3 className="text-lg font-bold text-green-400 font-mono">TERMINAL</h3>
              </div>
              
              <div className="space-y-2 text-sm font-mono">
                <div className="text-green-400">$ system_status --verbose</div>
                <div className="text-gray-400">Neural networks: ONLINE</div>
                <div className="text-gray-400">Learning modules: ACTIVE</div>
                <div className="text-gray-400">Data integrity: 99.7%</div>
                <div className="text-green-400">$ _</div>
              </div>
            </div>

            {/* Command Panel */}
            <div className="p-6 bg-black/60 border border-purple-500/30 rounded-lg backdrop-blur-sm">
              <h3 className="text-lg font-bold text-purple-400 font-mono mb-4">CMD_PANEL</h3>
              <div className="grid grid-cols-2 gap-3">
                {[
                  { cmd: "BOOST", icon: "⚡", color: "yellow" },
                  { cmd: "SCAN", icon: "🔍", color: "cyan" },
                  { cmd: "LEARN", icon: "🧠", color: "purple" },
                  { cmd: "SYNC", icon: "🔄", color: "green" }
                ].map((command, index) => (
                  <button
                    key={command.cmd}
                    className={cn(
                      "p-3 bg-black/40 border rounded font-mono text-sm transition-all duration-300 hover:scale-105",
                      `border-${command.color}-500/30 hover:border-${command.color}-500/60 text-${command.color}-400 hover:shadow-lg hover:shadow-${command.color}-500/30`
                    )}
                  >
                    <div className="text-lg mb-1">{command.icon}</div>
                    <div>{command.cmd}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* Data Stream */}
            <div className="p-6 bg-black/60 border border-pink-500/30 rounded-lg backdrop-blur-sm">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-3 h-3 bg-pink-500 rounded-full animate-pulse shadow-lg shadow-pink-500/50" />
                <h3 className="text-lg font-bold text-pink-400 font-mono">DATA_STREAM</h3>
              </div>
              
              <div className="space-y-2 text-xs font-mono">
                {[
                  "Incoming: neural_pattern_0x7f3a",
                  "Processing: knowledge_node_0x4b2c",
                  "Generated: insight_cluster_0x9e1d",
                  "Syncing: memory_bank_0x6a8f"
                ].map((stream, index) => (
                  <motion.div
                    key={index}
                    className="text-pink-300/80"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.5, repeat: Infinity, repeatDelay: 2 }}
                  >
                    {stream}
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
