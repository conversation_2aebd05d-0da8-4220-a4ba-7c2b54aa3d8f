// Test file to verify all imports work correctly
import React from 'react';

// Test individual theme imports
import { ModernGlassmorphismDashboard } from './ModernGlassmorphism';
import { NeumorphismDashboard } from './NeumorphismDesign';
import { CyberpunkNeonDashboard } from './CyberpunkNeon';
import { MinimalistCleanDashboard } from './MinimalistClean';
import { BrutalistBoldDashboard } from './BrutalistBold';

// Test theme config imports
import { 
  DESIGN_THEMES, 
  type DesignTheme, 
  type ThemeConfig,
  getThemeComponent,
  getThemeConfig,
  getAllThemes
} from './theme-config';

// Test theme hooks
import { useThemeSwitcher } from './theme-hooks';

// Test theme switcher components
import { ThemeSwitcher, ThemeDemo } from './ThemeSwitcher';

// Test main index exports
import * as DesignMockups from './index';

export function TestImports() {
  console.log('All imports successful!');
  console.log('Available themes:', getAllThemes());
  console.log('Design themes config:', DESIGN_THEMES);
  
  return (
    <div>
      <h1>Import Test Successful</h1>
      <p>All theme components and utilities imported correctly.</p>
    </div>
  );
}

export default TestImports;
