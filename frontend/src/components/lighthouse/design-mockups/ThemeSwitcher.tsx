import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '~/lib/ui-utils';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import {
  DESIGN_THEMES,
  type DesignTheme,
  type ThemeConfig
} from './theme-config';
import { useThemeSwitcher } from './theme-hooks';

interface ThemeSwitcherProps {
  onThemeChange?: (theme: DesignTheme) => void;
  className?: string;
}

interface ThemePreviewProps {
  theme: DesignTheme;
  config: ThemeConfig;
  isActive: boolean;
  onClick: () => void;
}

// Theme Preview Card Component
function ThemePreview({ theme, config, isActive, onClick }: ThemePreviewProps) {
  const getPreviewGradient = () => {
    const colors = config.colorPalette;
    return `linear-gradient(135deg, ${colors[0]} 0%, ${colors[1]} 50%, ${colors[2] || colors[0]} 100%)`;
  };

  return (
    <motion.div
      layout
      whileHover={{ scale: 1.02, y: -4 }}
      whileTap={{ scale: 0.98 }}
      className={cn(
        'cursor-pointer transition-all duration-300',
        isActive && 'ring-2 ring-primary ring-offset-2'
      )}
      onClick={onClick}
    >
      <Card className={cn(
        'overflow-hidden',
        isActive ? 'border-primary shadow-lg' : 'border-border hover:border-primary/50'
      )}>
        {/* Theme Preview */}
        <div 
          className="h-24 relative overflow-hidden"
          style={{ background: getPreviewGradient() }}
        >
          {/* Theme-specific preview elements */}
          {theme === 'glassmorphism' && (
            <div className="absolute inset-0 backdrop-blur-sm bg-white/20">
              <div className="absolute top-2 left-2 w-4 h-4 bg-white/40 rounded-full" />
              <div className="absolute bottom-2 right-2 w-6 h-6 bg-white/30 rounded-lg" />
            </div>
          )}
          
          {theme === 'neumorphism' && (
            <div className="absolute inset-0 bg-gray-100">
              <div className="absolute top-3 left-3 w-4 h-4 bg-gray-100 rounded-full shadow-[inset_2px_2px_4px_rgba(0,0,0,0.1),inset_-2px_-2px_4px_rgba(255,255,255,0.8)]" />
              <div className="absolute bottom-3 right-3 w-6 h-6 bg-gray-100 rounded-lg shadow-[2px_2px_4px_rgba(0,0,0,0.1),-2px_-2px_4px_rgba(255,255,255,0.8)]" />
            </div>
          )}
          
          {theme === 'cyberpunk' && (
            <div className="absolute inset-0 bg-black">
              <div className="absolute top-2 left-2 w-4 h-4 bg-cyan-500 rounded-full animate-pulse shadow-lg shadow-cyan-500/50" />
              <div className="absolute bottom-2 right-2 w-6 h-6 border-2 border-purple-500 rounded-lg animate-pulse" />
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-cyan-500/10 to-transparent animate-pulse" />
            </div>
          )}
          
          {theme === 'minimalist' && (
            <div className="absolute inset-0 bg-white">
              <div className="absolute top-4 left-4 w-8 h-px bg-gray-300" />
              <div className="absolute top-6 left-4 w-12 h-px bg-gray-200" />
              <div className="absolute bottom-4 right-4 w-3 h-3 border border-gray-300 rounded-full" />
            </div>
          )}
          
          {theme === 'brutalist' && (
            <div className="absolute inset-0 bg-yellow-400">
              <div className="absolute top-2 left-2 w-4 h-4 bg-red-500 border-2 border-black transform rotate-12" />
              <div className="absolute bottom-2 right-2 w-6 h-6 bg-blue-500 border-2 border-black transform -rotate-12" />
            </div>
          )}
        </div>

        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">{config.name}</CardTitle>
            {isActive && (
              <Badge variant="default" className="text-xs">
                Active
              </Badge>
            )}
          </div>
          <p className="text-sm text-muted-foreground">{config.description}</p>
        </CardHeader>

        <CardContent className="pt-0">
          {/* Characteristics */}
          <div className="space-y-3">
            <div>
              <p className="text-xs font-medium text-muted-foreground mb-2">Characteristics</p>
              <div className="flex flex-wrap gap-1">
                {config.characteristics.slice(0, 3).map((char, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {char}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Color Palette */}
            <div>
              <p className="text-xs font-medium text-muted-foreground mb-2">Colors</p>
              <div className="flex gap-1">
                {config.colorPalette.slice(0, 4).map((color, index) => (
                  <div
                    key={index}
                    className="w-4 h-4 rounded-full border border-border"
                    style={{ backgroundColor: color }}
                  />
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

// Main Theme Switcher Component
export function ThemeSwitcher({ onThemeChange, className }: ThemeSwitcherProps) {
  const { currentTheme, switchTheme, availableThemes } = useThemeSwitcher();

  const handleThemeChange = (theme: DesignTheme) => {
    switchTheme(theme);
    onThemeChange?.(theme);
  };

  return (
    <div className={cn('space-y-6', className)}>
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold">Choose Your Design Theme</h2>
        <p className="text-muted-foreground">
          Select a visual style that matches your workflow and preferences
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <AnimatePresence mode="wait">
          {availableThemes.map((theme) => (
            <motion.div
              key={theme}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <ThemePreview
                theme={theme}
                config={DESIGN_THEMES[theme]}
                isActive={currentTheme === theme}
                onClick={() => handleThemeChange(theme)}
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Current Theme Info */}
      <motion.div
        key={currentTheme}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className="mt-8 p-6 bg-muted/50 rounded-lg"
      >
        <div className="text-center space-y-3">
          <h3 className="text-lg font-semibold">
            Current Theme: {DESIGN_THEMES[currentTheme].name}
          </h3>
          <p className="text-sm text-muted-foreground">
            {DESIGN_THEMES[currentTheme].description}
          </p>
          
          <div className="space-y-2">
            <p className="text-xs font-medium text-muted-foreground">Best for:</p>
            <div className="flex flex-wrap justify-center gap-2">
              {DESIGN_THEMES[currentTheme].bestFor.map((use, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {use}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}

// Demo Component that shows the current theme
export function ThemeDemo() {
  const { getCurrentComponent } = useThemeSwitcher();
  const CurrentThemeComponent = getCurrentComponent();

  return (
    <div className="space-y-8">
      <ThemeSwitcher />
      
      <div className="border-t pt-8">
        <div className="text-center mb-6">
          <h3 className="text-xl font-semibold">Live Preview</h3>
          <p className="text-sm text-muted-foreground">
            See how your selected theme looks in action
          </p>
        </div>
        
        <div className="border rounded-lg overflow-hidden">
          <CurrentThemeComponent />
        </div>
      </div>
    </div>
  );
}
