// Design Mockup Exports
export { ModernGlassmorphismDashboard } from './ModernGlassmorphism';
export { NeumorphismDashboard } from './NeumorphismDesign';
export { CyberpunkNeonDashboard } from './CyberpunkNeon';
export { MinimalistCleanDashboard } from './MinimalistClean';
export { BrutalistBoldDashboard } from './BrutalistBold';

// Design Theme Types
export type DesignTheme = 
  | 'glassmorphism'
  | 'neumorphism' 
  | 'cyberpunk'
  | 'minimalist'
  | 'brutalist';

// Theme Configuration
export interface ThemeConfig {
  name: string;
  description: string;
  component: React.ComponentType;
  colorPalette: string[];
  characteristics: string[];
  bestFor: string[];
}

// Available Themes
export const DESIGN_THEMES: Record<DesignTheme, ThemeConfig> = {
  glassmorphism: {
    name: 'Modern Glassmorphism',
    description: 'Contemporary design with frosted glass effects and depth',
    component: ModernGlassmorphismDashboard,
    colorPalette: ['#3b82f6', '#8b5cf6', '#06b6d4', '#ffffff'],
    characteristics: ['Translucent', 'Depth', 'Soft gradients', 'Backdrop blur'],
    bestFor: ['Professional environments', 'Modern applications', 'Creative workflows']
  },
  neumorphism: {
    name: 'Neumorphism Design',
    description: 'Soft, tactile interfaces with embossed/debossed effects',
    component: NeumorphismDashboard,
    colorPalette: ['#f3f4f6', '#9ca3af', '#6366f1', '#000000'],
    characteristics: ['Soft shadows', 'Tactile feel', 'Monochromatic', 'Button-like'],
    bestFor: ['Touch interfaces', 'Mobile applications', 'Accessible design']
  },
  cyberpunk: {
    name: 'Cyberpunk Neon',
    description: 'Futuristic design with neon accents and dark themes',
    component: CyberpunkNeonDashboard,
    colorPalette: ['#00ffff', '#8b5cf6', '#10b981', '#ec4899'],
    characteristics: ['Neon glows', 'Dark backgrounds', 'High contrast', 'Futuristic'],
    bestFor: ['Gaming interfaces', 'Tech demos', 'Developer tools']
  },
  minimalist: {
    name: 'Minimalist Clean',
    description: 'Clean, spacious design focused on content and readability',
    component: MinimalistCleanDashboard,
    colorPalette: ['#ffffff', '#f9fafb', '#6b7280', '#111827'],
    characteristics: ['White space', 'Light typography', 'Subtle', 'Content-first'],
    bestFor: ['Research platforms', 'Academic tools', 'Reading interfaces']
  },
  brutalist: {
    name: 'Brutalist Bold',
    description: 'Bold, geometric design with high contrast and strong typography',
    component: BrutalistBoldDashboard,
    colorPalette: ['#ef4444', '#eab308', '#3b82f6', '#000000'],
    characteristics: ['Bold shapes', 'High contrast', 'Strong typography', 'Geometric'],
    bestFor: ['Art platforms', 'Creative tools', 'Statement interfaces']
  }
};

// Theme Utility Functions
export const getThemeComponent = (theme: DesignTheme) => {
  return DESIGN_THEMES[theme].component;
};

export const getThemeConfig = (theme: DesignTheme) => {
  return DESIGN_THEMES[theme];
};

export const getAllThemes = () => {
  return Object.keys(DESIGN_THEMES) as DesignTheme[];
};

// Theme Switcher Hook
import { useState, useCallback } from 'react';

export const useThemeSwitcher = (defaultTheme: DesignTheme = 'glassmorphism') => {
  const [currentTheme, setCurrentTheme] = useState<DesignTheme>(defaultTheme);
  
  const switchTheme = useCallback((theme: DesignTheme) => {
    setCurrentTheme(theme);
  }, []);
  
  const getCurrentComponent = useCallback(() => {
    return getThemeComponent(currentTheme);
  }, [currentTheme]);
  
  const getCurrentConfig = useCallback(() => {
    return getThemeConfig(currentTheme);
  }, [currentTheme]);
  
  return {
    currentTheme,
    switchTheme,
    getCurrentComponent,
    getCurrentConfig,
    availableThemes: getAllThemes()
  };
};
