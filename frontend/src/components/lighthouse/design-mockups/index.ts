// Design Mockup Exports
export { ModernGlassmorphismDashboard } from './ModernGlassmorphism';
export { NeumorphismDashboard } from './NeumorphismDesign';
export { CyberpunkNeonDashboard } from './CyberpunkNeon';
export { MinimalistCleanDashboard } from './MinimalistClean';
export { BrutalistBoldDashboard } from './BrutalistBold';

// Theme Configuration Exports
export * from './theme-config';

// Theme Switcher Components
export { ThemeSwitcher, ThemeDemo } from './ThemeSwitcher';

// Theme Switcher Hook
import { useState, useCallback } from 'react';

export const useThemeSwitcher = (defaultTheme: DesignTheme = 'glassmorphism') => {
  const [currentTheme, setCurrentTheme] = useState<DesignTheme>(defaultTheme);
  
  const switchTheme = useCallback((theme: DesignTheme) => {
    setCurrentTheme(theme);
  }, []);
  
  const getCurrentComponent = useCallback(() => {
    return getThemeComponent(currentTheme);
  }, [currentTheme]);
  
  const getCurrentConfig = useCallback(() => {
    return getThemeConfig(currentTheme);
  }, [currentTheme]);
  
  return {
    currentTheme,
    switchTheme,
    getCurrentComponent,
    getCurrentConfig,
    availableThemes: getAllThemes()
  };
};
