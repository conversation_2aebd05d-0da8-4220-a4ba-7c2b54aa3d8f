import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '~/lib/ui-utils';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';

// Neumorphism Design Mockup
export function NeumorphismDashboard() {
  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-gray-100 dark:bg-gray-900 shadow-neumorphism-inset dark:shadow-neumorphism-inset-dark">
        <div className="max-w-7xl mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gray-100 dark:bg-gray-900 rounded-2xl shadow-neumorphism dark:shadow-neumorphism-dark flex items-center justify-center">
                <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-200">Lighthouse AI</h1>
                <p className="text-gray-600 dark:text-gray-400">Neural Research Platform</p>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="px-4 py-2 bg-gray-100 dark:bg-gray-900 rounded-full shadow-neumorphism-inset dark:shadow-neumorphism-inset-dark">
                <span className="text-sm font-medium text-green-600 dark:text-green-400">● AI Online</span>
              </div>
              <button className="px-6 py-3 bg-gray-100 dark:bg-gray-900 rounded-2xl shadow-neumorphism dark:shadow-neumorphism-dark hover:shadow-neumorphism-pressed dark:hover:shadow-neumorphism-pressed-dark transition-all duration-200 text-gray-800 dark:text-gray-200 font-medium">
                New Project
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-6 py-8">
        {/* Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {[
            { title: "Neural Learning", value: "94%", subtitle: "Cognitive Progress", icon: "🧠" },
            { title: "Knowledge Nodes", value: "2,847", subtitle: "Connected Concepts", icon: "🔗" },
            { title: "Active Processes", value: "7", subtitle: "Running Agents", icon: "⚡" },
            { title: "Insights Today", value: "23", subtitle: "Generated Patterns", icon: "💎" }
          ].map((metric, index) => (
            <motion.div
              key={metric.title}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.1, duration: 0.5 }}
              className="group"
            >
              <div className="p-8 bg-gray-100 dark:bg-gray-900 rounded-3xl shadow-neumorphism dark:shadow-neumorphism-dark hover:shadow-neumorphism-hover dark:hover:shadow-neumorphism-hover-dark transition-all duration-300 cursor-pointer">
                <div className="flex items-center justify-between mb-6">
                  <div className="text-3xl">{metric.icon}</div>
                  <div className="w-12 h-12 bg-gray-100 dark:bg-gray-900 rounded-2xl shadow-neumorphism-inset dark:shadow-neumorphism-inset-dark flex items-center justify-center">
                    <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse" />
                  </div>
                </div>
                <div>
                  <p className="text-3xl font-bold text-gray-800 dark:text-gray-200 mb-2">{metric.value}</p>
                  <p className="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-1">{metric.title}</p>
                  <p className="text-sm text-gray-500 dark:text-gray-500">{metric.subtitle}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Main Dashboard Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Central Intelligence Hub */}
          <div className="lg:col-span-2">
            <div className="p-8 bg-gray-100 dark:bg-gray-900 rounded-3xl shadow-neumorphism dark:shadow-neumorphism-dark">
              <div className="flex items-center gap-4 mb-8">
                <div className="w-10 h-10 bg-gray-100 dark:bg-gray-900 rounded-2xl shadow-neumorphism-inset dark:shadow-neumorphism-inset-dark flex items-center justify-center">
                  <span className="text-xl">🎯</span>
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200">Intelligence Center</h2>
                  <p className="text-gray-600 dark:text-gray-400">Real-time cognitive processing</p>
                </div>
              </div>

              {/* Neural Network Visualization */}
              <div className="relative h-80 bg-gray-100 dark:bg-gray-900 rounded-2xl shadow-neumorphism-inset dark:shadow-neumorphism-inset-dark mb-8 overflow-hidden">
                <div className="absolute inset-6">
                  {/* Neural Nodes */}
                  {[
                    { x: '15%', y: '25%', size: 'w-6 h-6', pulse: true },
                    { x: '35%', y: '15%', size: 'w-8 h-8', pulse: false },
                    { x: '55%', y: '35%', size: 'w-10 h-10', pulse: true },
                    { x: '75%', y: '20%', size: 'w-7 h-7', pulse: false },
                    { x: '25%', y: '65%', size: 'w-5 h-5', pulse: true },
                    { x: '65%', y: '70%', size: 'w-9 h-9', pulse: false },
                    { x: '85%', y: '60%', size: 'w-6 h-6', pulse: true }
                  ].map((node, index) => (
                    <motion.div
                      key={index}
                      className={cn(
                        "absolute rounded-full bg-gray-100 dark:bg-gray-900 shadow-neumorphism dark:shadow-neumorphism-dark flex items-center justify-center",
                        node.size
                      )}
                      style={{ left: node.x, top: node.y }}
                      animate={node.pulse ? {
                        boxShadow: [
                          '4px 4px 8px #d1d5db, -4px -4px 8px #ffffff',
                          '6px 6px 12px #d1d5db, -6px -6px 12px #ffffff',
                          '4px 4px 8px #d1d5db, -4px -4px 8px #ffffff'
                        ]
                      } : {}}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        delay: index * 0.3
                      }}
                    >
                      <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full" />
                    </motion.div>
                  ))}

                  {/* Connection Lines */}
                  <svg className="absolute inset-0 w-full h-full">
                    <defs>
                      <linearGradient id="neuralGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="#6366f1" stopOpacity="0.4" />
                        <stop offset="100%" stopColor="#8b5cf6" stopOpacity="0.4" />
                      </linearGradient>
                    </defs>
                    <path d="M 15% 25% Q 25% 20% 35% 15%" stroke="url(#neuralGradient)" strokeWidth="2" fill="none" strokeDasharray="5,5">
                      <animate attributeName="stroke-dashoffset" values="0;10" dur="2s" repeatCount="indefinite" />
                    </path>
                    <path d="M 35% 15% Q 45% 25% 55% 35%" stroke="url(#neuralGradient)" strokeWidth="2" fill="none" strokeDasharray="5,5">
                      <animate attributeName="stroke-dashoffset" values="0;10" dur="2s" repeatCount="indefinite" begin="0.5s" />
                    </path>
                    <path d="M 55% 35% Q 65% 30% 75% 20%" stroke="url(#neuralGradient)" strokeWidth="2" fill="none" strokeDasharray="5,5">
                      <animate attributeName="stroke-dashoffset" values="0;10" dur="2s" repeatCount="indefinite" begin="1s" />
                    </path>
                    <path d="M 25% 65% Q 45% 50% 65% 70%" stroke="url(#neuralGradient)" strokeWidth="2" fill="none" strokeDasharray="5,5">
                      <animate attributeName="stroke-dashoffset" values="0;10" dur="2s" repeatCount="indefinite" begin="1.5s" />
                    </path>
                  </svg>
                </div>

                <div className="absolute bottom-4 left-4">
                  <div className="px-4 py-2 bg-gray-100 dark:bg-gray-900 rounded-xl shadow-neumorphism-inset dark:shadow-neumorphism-inset-dark">
                    <p className="text-sm font-semibold text-gray-700 dark:text-gray-300">Neural Activity</p>
                    <p className="text-xs text-gray-500 dark:text-gray-500">Processing 847 connections/sec</p>
                  </div>
                </div>
              </div>

              {/* Processing Queue */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">Active Processes</h3>
                {[
                  { task: "Analyzing research patterns", progress: 78, status: "processing" },
                  { task: "Generating knowledge connections", progress: 45, status: "processing" },
                  { task: "Optimizing neural pathways", progress: 92, status: "completing" }
                ].map((process, index) => (
                  <div key={index} className="p-4 bg-gray-100 dark:bg-gray-900 rounded-2xl shadow-neumorphism-inset dark:shadow-neumorphism-inset-dark">
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{process.task}</span>
                      <span className="text-sm text-gray-500 dark:text-gray-500">{process.progress}%</span>
                    </div>
                    <div className="w-full h-2 bg-gray-100 dark:bg-gray-900 rounded-full shadow-neumorphism-inset dark:shadow-neumorphism-inset-dark overflow-hidden">
                      <motion.div
                        className="h-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: `${process.progress}%` }}
                        transition={{ duration: 1, delay: index * 0.2 }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Control Panel */}
          <div className="space-y-8">
            {/* System Status */}
            <div className="p-6 bg-gray-100 dark:bg-gray-900 rounded-3xl shadow-neumorphism dark:shadow-neumorphism-dark">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-gray-100 dark:bg-gray-900 rounded-xl shadow-neumorphism-inset dark:shadow-neumorphism-inset-dark flex items-center justify-center">
                  <span className="text-lg">⚙️</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">System Status</h3>
              </div>
              
              <div className="space-y-4">
                {[
                  { label: "Neural Processing", status: "Optimal", color: "green" },
                  { label: "Memory Usage", status: "Normal", color: "blue" },
                  { label: "Learning Rate", status: "High", color: "purple" }
                ].map((item, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">{item.label}</span>
                    <div className={cn(
                      "px-3 py-1 rounded-full text-xs font-medium shadow-neumorphism-inset dark:shadow-neumorphism-inset-dark",
                      item.color === 'green' ? 'text-green-600 dark:text-green-400' :
                      item.color === 'blue' ? 'text-blue-600 dark:text-blue-400' :
                      'text-purple-600 dark:text-purple-400'
                    )}>
                      {item.status}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Quick Controls */}
            <div className="p-6 bg-gray-100 dark:bg-gray-900 rounded-3xl shadow-neumorphism dark:shadow-neumorphism-dark">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-6">Quick Controls</h3>
              <div className="grid grid-cols-2 gap-4">
                {[
                  { label: "Boost", icon: "🚀" },
                  { label: "Analyze", icon: "🔍" },
                  { label: "Learn", icon: "📚" },
                  { label: "Connect", icon: "🔗" }
                ].map((control, index) => (
                  <button
                    key={control.label}
                    className="p-4 bg-gray-100 dark:bg-gray-900 rounded-2xl shadow-neumorphism dark:shadow-neumorphism-dark hover:shadow-neumorphism-pressed dark:hover:shadow-neumorphism-pressed-dark transition-all duration-200 flex flex-col items-center gap-2"
                  >
                    <span className="text-2xl">{control.icon}</span>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{control.label}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
