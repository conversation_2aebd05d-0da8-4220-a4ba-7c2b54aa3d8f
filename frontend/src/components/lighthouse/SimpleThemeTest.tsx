import React, { useState } from 'react';
import { Button } from '~/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '~/components/ui/card';

// Very simple theme switcher test without complex dependencies
export function SimpleThemeTest() {
  const [currentTheme, setCurrentTheme] = useState('glassmorphism');
  const [showModal, setShowModal] = useState(false);

  const themes = [
    { id: 'glassmorphism', name: 'Glassmorphism', color: '#3b82f6' },
    { id: 'cyberpunk', name: 'Cyberpunk', color: '#00ffff' },
    { id: 'minimalist', name: 'Minimalist', color: '#6b7280' },
    { id: 'brutalist', name: '<PERSON>rutalist', color: '#ef4444' },
    { id: 'neumorphism', name: 'Neumorphism', color: '#9ca3af' }
  ];

  const currentThemeData = themes.find(t => t.id === currentTheme);

  return (
    <div className="min-h-screen bg-gray-50 relative">
      {/* Simple Theme Switcher Button - Top Right */}
      <div className="fixed top-4 right-4 z-50">
        <Button
          onClick={() => setShowModal(true)}
          className="bg-white border-2 border-gray-300 text-gray-800 hover:bg-gray-50 shadow-lg"
        >
          <div className="flex items-center gap-2">
            <div
              className="w-4 h-4 rounded-full border-2 border-gray-400"
              style={{ backgroundColor: currentThemeData?.color }}
            />
            <span className="font-medium">Themes</span>
          </div>
        </Button>
      </div>

      {/* Theme Selection Modal */}
      {showModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50">
          <Card className="w-full max-w-2xl">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Choose Theme</CardTitle>
                <Button
                  variant="ghost"
                  onClick={() => setShowModal(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  ✕
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {themes.map((theme) => (
                  <button
                    key={theme.id}
                    onClick={() => {
                      setCurrentTheme(theme.id);
                      setShowModal(false);
                    }}
                    className={`p-4 rounded-lg border-2 transition-all ${
                      currentTheme === theme.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div
                      className="w-full h-16 rounded mb-3"
                      style={{ backgroundColor: theme.color }}
                    />
                    <div className="font-medium text-sm">{theme.name}</div>
                    {currentTheme === theme.id && (
                      <div className="text-xs text-blue-600 mt-1">Active</div>
                    )}
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <div className="p-8">
        <Card className="max-w-4xl mx-auto">
          <CardHeader>
            <CardTitle className="text-2xl">Simple Theme Switcher Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-2">Current Theme: {currentThemeData?.name}</h3>
              <div className="flex items-center gap-3">
                <div
                  className="w-8 h-8 rounded-full border-2 border-gray-300"
                  style={{ backgroundColor: currentThemeData?.color }}
                />
                <span className="text-gray-600">Theme color: {currentThemeData?.color}</span>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Instructions:</h3>
              <ol className="list-decimal list-inside space-y-2 text-gray-700">
                <li>Look for the <strong>"Themes"</strong> button in the <strong>top-right corner</strong></li>
                <li>It should show a colored circle and the word "Themes"</li>
                <li>Click it to open the theme selection modal</li>
                <li>Choose a different theme to see it change</li>
                <li>The button color should update to match the selected theme</li>
              </ol>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-semibold text-yellow-800 mb-2">🔍 Can't find the theme switcher?</h4>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>• Check the <strong>top-right corner</strong> of your screen</li>
                <li>• Look for a white button with a colored circle</li>
                <li>• It should be positioned above all other content</li>
                <li>• Try scrolling to the top of the page</li>
                <li>• Check if your browser window is wide enough</li>
              </ul>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-semibold text-blue-800 mb-2">✅ This is working correctly if:</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• You can see the "Themes" button in the top-right</li>
                <li>• Clicking it opens a modal with theme options</li>
                <li>• Selecting a theme changes the button color</li>
                <li>• The modal closes after selecting a theme</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default SimpleThemeTest;
