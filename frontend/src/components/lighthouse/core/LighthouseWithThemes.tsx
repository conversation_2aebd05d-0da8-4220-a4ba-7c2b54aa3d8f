import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '~/lib/ui-utils';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { 
  useThemeSwitcher, 
  ThemeSwitcher,
  type DesignTheme,
  DESIGN_THEMES 
} from '../design-mockups';
import { useLighthouseStore } from '../shared/store/lighthouse-store';
import { AccessibilityUtils } from '@/utils/accessibilityUtils';

interface LighthouseWithThemesProps {
  defaultTheme?: DesignTheme;
  showThemeSwitcher?: boolean;
  className?: string;
}

export function LighthouseWithThemes({ 
  defaultTheme = 'glassmorphism',
  showThemeSwitcher = true,
  className 
}: LighthouseWithThemesProps) {
  const { currentProject } = useLighthouseStore();
  const { currentTheme, switchTheme, getCurrentComponent, availableThemes } = useThemeSwitcher(defaultTheme);
  const [isThemeSwitcherOpen, setIsThemeSwitcherOpen] = useState(false);
  const [hasSeenThemeSwitcher, setHasSeenThemeSwitcher] = useState(false);
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();

  // Check if user has seen theme switcher before
  useEffect(() => {
    const seen = localStorage.getItem('lighthouse-theme-switcher-seen');
    setHasSeenThemeSwitcher(!!seen);
  }, []);

  // Save theme preference
  useEffect(() => {
    localStorage.setItem('lighthouse-preferred-theme', currentTheme);
  }, [currentTheme]);

  // Load saved theme preference
  useEffect(() => {
    const savedTheme = localStorage.getItem('lighthouse-preferred-theme') as DesignTheme;
    if (savedTheme && availableThemes.includes(savedTheme)) {
      switchTheme(savedTheme);
    }
  }, [switchTheme, availableThemes]);

  const handleThemeChange = (theme: DesignTheme) => {
    switchTheme(theme);
    setIsThemeSwitcherOpen(false);
  };

  const handleThemeSwitcherClose = () => {
    setIsThemeSwitcherOpen(false);
    if (!hasSeenThemeSwitcher) {
      localStorage.setItem('lighthouse-theme-switcher-seen', 'true');
      setHasSeenThemeSwitcher(true);
    }
  };

  const CurrentThemeComponent = getCurrentComponent();

  return (
    <div className={cn('relative min-h-screen', className)}>
      {/* Theme Switcher Toggle Button */}
      {showThemeSwitcher && (
        <motion.div
          className="fixed top-4 right-4 z-50"
          initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, scale: 0.8 }}
          animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, scale: 1 }}
          transition={{ duration: prefersReducedMotion ? 0.1 : 0.3, delay: 1 }}
        >
          <Button
            onClick={() => setIsThemeSwitcherOpen(true)}
            variant="outline"
            size="sm"
            className={cn(
              'backdrop-blur-sm bg-background/80 border-border/50 hover:bg-background/90',
              'shadow-lg hover:shadow-xl transition-all duration-300',
              'flex items-center gap-2'
            )}
            aria-label="Open theme switcher"
          >
            <div className="flex items-center gap-1">
              {DESIGN_THEMES[currentTheme].colorPalette.slice(0, 3).map((color, index) => (
                <div
                  key={index}
                  className="w-3 h-3 rounded-full border border-border/30"
                  style={{ backgroundColor: color }}
                />
              ))}
            </div>
            <span className="text-sm font-medium">Themes</span>
          </Button>

          {/* New User Hint */}
          {!hasSeenThemeSwitcher && (
            <motion.div
              className="absolute -bottom-12 right-0 px-3 py-2 bg-primary text-primary-foreground rounded-lg text-xs font-medium shadow-lg"
              initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, y: -10 }}
              animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, y: 0 }}
              transition={{ duration: prefersReducedMotion ? 0.1 : 0.3, delay: 2 }}
            >
              Try different themes!
              <div className="absolute top-0 right-4 w-0 h-0 border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-primary transform -translate-y-full" />
            </motion.div>
          )}
        </motion.div>
      )}

      {/* Theme Switcher Modal */}
      <AnimatePresence>
        {isThemeSwitcherOpen && (
          <motion.div
            className="fixed inset-0 z-50 flex items-center justify-center p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: prefersReducedMotion ? 0.1 : 0.2 }}
          >
            {/* Backdrop */}
            <motion.div
              className="absolute inset-0 bg-black/50 backdrop-blur-sm"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={handleThemeSwitcherClose}
            />

            {/* Modal Content */}
            <motion.div
              className="relative w-full max-w-6xl max-h-[90vh] overflow-y-auto bg-background rounded-xl shadow-2xl border"
              initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, scale: 0.9, y: 20 }}
              animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, scale: 1, y: 0 }}
              exit={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, scale: 0.9, y: 20 }}
              transition={{ duration: prefersReducedMotion ? 0.1 : 0.3 }}
            >
              <div className="p-6">
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <h2 className="text-2xl font-bold">Choose Your Theme</h2>
                    <p className="text-muted-foreground">
                      Customize Lighthouse's appearance to match your workflow
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleThemeSwitcherClose}
                    className="text-muted-foreground hover:text-foreground"
                  >
                    ✕
                  </Button>
                </div>

                {/* Current Theme Info */}
                <div className="mb-8 p-4 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className="flex gap-1">
                      {DESIGN_THEMES[currentTheme].colorPalette.map((color, index) => (
                        <div
                          key={index}
                          className="w-6 h-6 rounded-full border-2 border-background shadow-sm"
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                    <div>
                      <h3 className="font-semibold">{DESIGN_THEMES[currentTheme].name}</h3>
                      <p className="text-sm text-muted-foreground">
                        {DESIGN_THEMES[currentTheme].description}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Theme Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {availableThemes.map((theme, index) => {
                    const config = DESIGN_THEMES[theme];
                    const isActive = currentTheme === theme;

                    return (
                      <motion.div
                        key={theme}
                        initial={prefersReducedMotion ? { opacity: 0 } : { opacity: 0, y: 20 }}
                        animate={prefersReducedMotion ? { opacity: 1 } : { opacity: 1, y: 0 }}
                        transition={{ duration: prefersReducedMotion ? 0.1 : 0.3, delay: index * 0.1 }}
                      >
                        <Card
                          className={cn(
                            'cursor-pointer transition-all duration-300 hover:shadow-lg',
                            isActive ? 'ring-2 ring-primary shadow-lg' : 'hover:border-primary/50'
                          )}
                          onClick={() => handleThemeChange(theme)}
                        >
                          {/* Theme Preview */}
                          <div className="h-24 relative overflow-hidden rounded-t-lg">
                            <div
                              className="absolute inset-0"
                              style={{
                                background: `linear-gradient(135deg, ${config.colorPalette[0]} 0%, ${config.colorPalette[1]} 50%, ${config.colorPalette[2] || config.colorPalette[0]} 100%)`
                              }}
                            />
                            {/* Theme-specific preview elements */}
                            {theme === 'glassmorphism' && (
                              <div className="absolute inset-0 backdrop-blur-sm bg-white/20">
                                <div className="absolute top-2 left-2 w-4 h-4 bg-white/40 rounded-full" />
                                <div className="absolute bottom-2 right-2 w-6 h-6 bg-white/30 rounded-lg" />
                              </div>
                            )}
                            {theme === 'cyberpunk' && (
                              <div className="absolute inset-0 bg-black/80">
                                <div className="absolute top-2 left-2 w-4 h-4 bg-cyan-500 rounded-full animate-pulse" />
                                <div className="absolute bottom-2 right-2 w-6 h-6 border-2 border-purple-500 rounded-lg" />
                              </div>
                            )}
                            {theme === 'minimalist' && (
                              <div className="absolute inset-0 bg-white">
                                <div className="absolute top-4 left-4 w-8 h-px bg-gray-300" />
                                <div className="absolute bottom-4 right-4 w-3 h-3 border border-gray-300 rounded-full" />
                              </div>
                            )}
                          </div>

                          <CardHeader className="pb-3">
                            <div className="flex items-center justify-between">
                              <CardTitle className="text-lg">{config.name}</CardTitle>
                              {isActive && (
                                <Badge variant="default" className="text-xs">
                                  Active
                                </Badge>
                              )}
                            </div>
                            <p className="text-sm text-muted-foreground">{config.description}</p>
                          </CardHeader>

                          <CardContent className="pt-0">
                            <div className="space-y-3">
                              <div>
                                <p className="text-xs font-medium text-muted-foreground mb-2">Best for</p>
                                <div className="flex flex-wrap gap-1">
                                  {config.bestFor.slice(0, 2).map((use, idx) => (
                                    <Badge key={idx} variant="secondary" className="text-xs">
                                      {use}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    );
                  })}
                </div>

                {/* Footer */}
                <div className="mt-8 pt-6 border-t text-center">
                  <p className="text-sm text-muted-foreground">
                    Your theme preference will be saved automatically
                  </p>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Application with Selected Theme */}
      <div className="relative">
        <CurrentThemeComponent />
      </div>
    </div>
  );
}
