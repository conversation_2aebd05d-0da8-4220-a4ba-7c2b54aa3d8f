import React, { useEffect, useState, Suspense } from 'react';
import { cn, layout, animations } from '~/lib/ui-utils';
import { LoadingOverlay } from '~/components/ui/loading-states';
import { ModuleSkeleton } from '../shared/EnhancedLoadingStates';
import { PageErrorBoundary, SectionErrorBoundary } from '~/components/ui/error-boundary';
import { ResponsiveSidebar, useBreakpoint, AdaptiveLayout } from '~/components/ui/responsive-layout';
import { useLighthouseStore } from '../shared/store/lighthouse-store';
import { ModuleName, MODULE_ROUTES } from '../types/navigation.types';
import { ProjectContext } from './ProjectContext';
import { NavigationSystem } from './NavigationSystem';
import { ModuleContainer } from './ModuleContainer';
import { useThemeSwitcher, type DesignTheme } from '../design-mockups';

// Module imports - these will be created
import { Dashboard } from '../modules/dashboard/Dashboard';
import { KnowledgeHub } from '../modules/knowledge/KnowledgeHub';
import { ResearchStudio } from '../modules/research/ResearchStudio';
import { AgentWorkspace } from '../modules/agents/AgentWorkspace';
import { ContextualChat } from '../modules/chat/ContextualChat';
import { SourceManager } from '../modules/sources/SourceManager';
import { SystemAnalytics } from '../modules/analytics/SystemAnalytics';
import { AIInsights } from '../modules/insights/AIInsights';

interface LighthouseMainProps {
  initialModule?: ModuleName;
  className?: string;
  enableThemeSwitcher?: boolean;
  defaultTheme?: DesignTheme;
}

const moduleComponents: Record<ModuleName, React.ComponentType<any>> = {
  dashboard: Dashboard,
  knowledge: KnowledgeHub,
  research: ResearchStudio,
  agents: AgentWorkspace,
  chat: ContextualChat,
  sources: SourceManager,
  analytics: SystemAnalytics,
  insights: AIInsights,
};

export function LighthouseMain({
  initialModule = 'dashboard',
  className,
  enableThemeSwitcher = false,
  defaultTheme = 'glassmorphism'
}: LighthouseMainProps) {
  const {
    navigation,
    currentProject,
    projectContext,
    intelligenceContext,
    setCurrentModule,
    initializeSession,
    updateIntelligenceContext,
  } = useLighthouseStore();

  const { currentTheme, getCurrentComponent } = useThemeSwitcher(defaultTheme);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const breakpoint = useBreakpoint();

  // Initialize session on mount
  useEffect(() => {
    initializeSession();
    setIsLoading(false);
  }, [initializeSession]);

  // Set initial module
  useEffect(() => {
    if (initialModule && initialModule !== navigation.currentModule) {
      setCurrentModule(initialModule);
    }
  }, [initialModule, setCurrentModule, navigation.currentModule]);

  // Update intelligence context when project changes
  useEffect(() => {
    if (currentProject && !intelligenceContext) {
      updateIntelligenceContext({
        projectId: currentProject.id,
        currentFocus: `Working on ${currentProject.name}`,
      });
    }
  }, [currentProject, intelligenceContext, updateIntelligenceContext]);

  if (isLoading) {
    return (
      <LoadingOverlay 
        loading={true}
        message="Initializing Lighthouse workspace..."
        spinnerSize="xl"
        backdrop="solid"
        className={cn('h-screen', className)}
      >
        <div />
      </LoadingOverlay>
    );
  }

  if (error) {
    return (
      <PageErrorBoundary
        onError={(err, info) => {
          console.error('Lighthouse initialization error:', err, info);
        }}
        className={className}
      >
        <div className={cn(layout.flexColCenter, 'h-screen')}>
          <div className="text-center space-y-4 max-w-md">
            <div className="text-destructive text-xl">⚠️ Initialization Error</div>
            <p className="text-muted-foreground">{error}</p>
          </div>
        </div>
      </PageErrorBoundary>
    );
  }

  const CurrentModule = moduleComponents[navigation.currentModule];

  return (
    <PageErrorBoundary
      onError={(error, errorInfo) => {
        // Log error to monitoring service
        console.error('Lighthouse error:', error, errorInfo);
      }}
    >
      <div className={cn(
        'flex flex-col h-screen bg-background transition-all duration-200',
        className
      )}>
        {/* Project Context Bar */}
        <SectionErrorBoundary level="section">
          <Suspense fallback={<div className="h-16 bg-muted/20 animate-pulse" />}>
            <ProjectContext />
          </Suspense>
        </SectionErrorBoundary>
        
        {/* Main Layout - Adaptive based on screen size */}
        <AdaptiveLayout
          mobile={
            // Mobile: Full-screen with overlay navigation
            <div className="flex flex-1 overflow-hidden relative">
              <ResponsiveSidebar
                open={sidebarOpen}
                onOpenChange={setSidebarOpen}
                title="Lighthouse Navigation"
                side="left"
              >
                <SectionErrorBoundary level="section">
                  <Suspense fallback={<div className="space-y-4 p-4">{[...Array(6)].map((_, i) => <div key={i} className="h-10 bg-muted rounded animate-pulse" />)}</div>}>
                    <NavigationSystem />
                  </Suspense>
                </SectionErrorBoundary>
              </ResponsiveSidebar>
              
              <main 
                className="flex-1 overflow-hidden"
                role="main"
                aria-label={`${MODULE_ROUTES[navigation.currentModule].label} module`}
              >
                <SectionErrorBoundary level="section">
                  <Suspense fallback={<ModuleSkeleton module={navigation.currentModule} />}>
                    <ModuleContainer
                      module={navigation.currentModule}
                      moduleInfo={MODULE_ROUTES[navigation.currentModule]}
                    >
                      <div className={animations.fadeIn}>
                        <CurrentModule />
                      </div>
                    </ModuleContainer>
                  </Suspense>
                </SectionErrorBoundary>
              </main>
            </div>
          }
          
          tablet={
            // Tablet: Collapsible sidebar with more space
            <div className="flex flex-1 overflow-hidden">
              <ResponsiveSidebar
                open={sidebarOpen}
                onOpenChange={setSidebarOpen}
                title="Navigation"
                side="left"
                width="280px"
              >
                <SectionErrorBoundary level="section">
                  <Suspense fallback={<div className="w-full h-full bg-muted/50 animate-pulse" />}>
                    <NavigationSystem />
                  </Suspense>
                </SectionErrorBoundary>
              </ResponsiveSidebar>
              
              <main 
                className={cn(
                  'flex-1 overflow-hidden transition-all duration-200',
                  sidebarOpen ? 'ml-0' : 'ml-0'
                )}
                role="main"
                aria-label={`${MODULE_ROUTES[navigation.currentModule].label} module`}
              >
                <SectionErrorBoundary level="section">
                  <Suspense fallback={<ModuleSkeleton module={navigation.currentModule} />}>
                    <ModuleContainer
                      module={navigation.currentModule}
                      moduleInfo={MODULE_ROUTES[navigation.currentModule]}
                    >
                      <div className={animations.fadeIn}>
                        <CurrentModule />
                      </div>
                    </ModuleContainer>
                  </Suspense>
                </SectionErrorBoundary>
              </main>
            </div>
          }
          
          desktop={
            // Desktop: Fixed sidebar layout
            <div className="flex flex-1 overflow-hidden">
              {/* Fixed Navigation Sidebar */}
              <SectionErrorBoundary level="section">
                <Suspense fallback={<div className="w-64 border-r bg-muted/50 animate-pulse" />}>
                  <div className="w-64 border-r bg-muted/50 flex flex-col">
                    <NavigationSystem />
                  </div>
                </Suspense>
              </SectionErrorBoundary>
              
              {/* Module Content */}
              <main 
                className="flex-1 overflow-hidden"
                role="main"
                aria-label={`${MODULE_ROUTES[navigation.currentModule].label} module`}
              >
                <SectionErrorBoundary level="section">
                  <Suspense fallback={<ModuleSkeleton module={navigation.currentModule} />}>
                    <ModuleContainer
                      module={navigation.currentModule}
                      moduleInfo={MODULE_ROUTES[navigation.currentModule]}
                    >
                      <div className={animations.fadeIn}>
                        <CurrentModule />
                      </div>
                    </ModuleContainer>
                  </Suspense>
                </SectionErrorBoundary>
              </main>
            </div>
          }
          
          wide={
            // Wide screen: Enhanced layout with more space
            <div className="flex flex-1 overflow-hidden">
              {/* Enhanced Navigation Sidebar */}
              <SectionErrorBoundary level="section">
                <Suspense fallback={<div className="w-80 border-r bg-muted/50 animate-pulse" />}>
                  <div className="w-80 border-r bg-muted/50 flex flex-col">
                    <NavigationSystem />
                  </div>
                </Suspense>
              </SectionErrorBoundary>
              
              {/* Module Content with max width */}
              <main 
                className="flex-1 overflow-hidden flex justify-center"
                role="main"
                aria-label={`${MODULE_ROUTES[navigation.currentModule].label} module`}
              >
                <div className="w-full max-w-7xl">
                  <SectionErrorBoundary level="section">
                    <Suspense fallback={<ModuleSkeleton module={navigation.currentModule} />}>
                      <ModuleContainer
                        module={navigation.currentModule}
                        moduleInfo={MODULE_ROUTES[navigation.currentModule]}
                      >
                        <div className={animations.fadeIn}>
                          <CurrentModule />
                        </div>
                      </ModuleContainer>
                    </Suspense>
                  </SectionErrorBoundary>
                </div>
              </main>
            </div>
          }
        />
      </div>
    </PageErrorBoundary>
  );
}

// Export as default for route compatibility
export default LighthouseMain;