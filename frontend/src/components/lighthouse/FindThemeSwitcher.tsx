import React from 'react';
import { LighthouseWithThemes } from './core/LighthouseWithThemes';
import { ThemeSwitcherTest } from './debug/ThemeSwitcherDebug';

/**
 * Component to help you find the theme switcher
 * 
 * This will show you exactly where the theme switcher should appear
 * and help troubleshoot if it's not visible.
 */

export function FindThemeSwitcher() {
  const [showDebugMode, setShowDebugMode] = React.useState(true);

  if (showDebugMode) {
    return (
      <div>
        {/* Debug mode to show where theme switcher should be */}
        <ThemeSwitcherTest />
        
        <div className="fixed bottom-4 right-4 z-[9999]">
          <button
            onClick={() => setShowDebugMode(false)}
            className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg font-bold"
          >
            Switch to Real Theme Switcher
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Real LighthouseWithThemes component */}
      <LighthouseWithThemes
        defaultTheme="glassmorphism"
        showThemeSwitcher={true}
      />
      
      {/* Helper button to go back to debug mode */}
      <div className="fixed bottom-4 left-4 z-[9999]">
        <button
          onClick={() => setShowDebugMode(true)}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-bold text-sm"
        >
          Show Debug Mode
        </button>
      </div>
    </div>
  );
}

/**
 * USAGE INSTRUCTIONS:
 * 
 * 1. Import and use this component:
 *    import { FindThemeSwitcher } from '@/components/lighthouse/FindThemeSwitcher';
 *    <FindThemeSwitcher />
 * 
 * 2. You'll see a debug mode first with:
 *    - Red button showing exact position
 *    - Debug information panel
 *    - Instructions and troubleshooting
 * 
 * 3. Click "Switch to Real Theme Switcher" to see the actual component
 * 
 * 4. Look for the theme switcher button in the top-right corner:
 *    - Should appear as a button with 3 colored circles
 *    - Text should say "Themes"
 *    - Should fade in after 1 second
 * 
 * 5. If you don't see it, go back to debug mode for troubleshooting
 */

export default FindThemeSwitcher;
