import React from 'react';
import { LighthouseWithThemes } from './core/LighthouseWithThemes';

/**
 * Quick Start Example - Fixed Import Error
 * 
 * This is the simplest way to use the theme switcher.
 * All imports have been fixed and should work without errors.
 */

export function QuickStartExample() {
  return (
    <LighthouseWithThemes
      defaultTheme="glassmorphism"
      showThemeSwitcher={true}
    />
  );
}

/**
 * Alternative: Use the demo version with welcome screen
 */
export function DemoExample() {
  // Import the demo component
  const { LighthouseThemeDemo } = require('./demo/LighthouseThemeDemo');
  
  return <LighthouseThemeDemo />;
}

/**
 * Alternative: Production version with fixed theme
 */
export function ProductionExample() {
  return (
    <LighthouseWithThemes
      defaultTheme="minimalist"
      showThemeSwitcher={false}
    />
  );
}

/**
 * USAGE INSTRUCTIONS:
 * 
 * 1. Replace your current Lighthouse component with any of the above
 * 2. The theme switcher button will appear in the top-right corner
 * 3. Click it to see all available themes
 * 4. User preferences are automatically saved
 * 
 * INTEGRATION:
 * 
 * // In your App.tsx or main component:
 * import { QuickStartExample } from '@/components/lighthouse/QuickStartExample';
 * 
 * function App() {
 *   return <QuickStartExample />;
 * }
 */

export default QuickStartExample;
