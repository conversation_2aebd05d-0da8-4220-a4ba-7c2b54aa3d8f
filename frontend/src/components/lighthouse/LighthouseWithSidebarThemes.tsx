import React from 'react';
import { LighthouseWithThemes } from './core/LighthouseWithThemes';
import { type DesignTheme } from './design-mockups/theme-config';

/**
 * Lighthouse component with theme switcher integrated into the sidebar
 * 
 * This is the recommended way to use the theme switcher as it provides
 * a clean, integrated experience within the navigation sidebar.
 */

interface LighthouseWithSidebarThemesProps {
  defaultTheme?: DesignTheme;
  className?: string;
}

export function LighthouseWithSidebarThemes({ 
  defaultTheme = 'glassmorphism',
  className 
}: LighthouseWithSidebarThemesProps) {
  return (
    <LighthouseWithThemes
      defaultTheme={defaultTheme}
      showThemeSwitcher={true}
      themeSwitcherLocation="sidebar"
      className={className}
    />
  );
}

/**
 * Alternative: Lighthouse with floating theme switcher (original behavior)
 */
export function LighthouseWithFloatingThemes({ 
  defaultTheme = 'glassmorphism',
  className 
}: LighthouseWithSidebarThemesProps) {
  return (
    <LighthouseWithThemes
      defaultTheme={defaultTheme}
      showThemeSwitcher={true}
      themeSwitcherLocation="floating"
      className={className}
    />
  );
}

/**
 * Production version with no theme switcher
 */
export function LighthouseProduction({ 
  defaultTheme = 'glassmorphism',
  className 
}: LighthouseWithSidebarThemesProps) {
  return (
    <LighthouseWithThemes
      defaultTheme={defaultTheme}
      showThemeSwitcher={false}
      className={className}
    />
  );
}

/**
 * USAGE EXAMPLES:
 * 
 * // Recommended: Theme switcher in sidebar
 * <LighthouseWithSidebarThemes defaultTheme="glassmorphism" />
 * 
 * // Alternative: Floating theme switcher
 * <LighthouseWithFloatingThemes defaultTheme="cyberpunk" />
 * 
 * // Production: No theme switcher
 * <LighthouseProduction defaultTheme="minimalist" />
 */

export default LighthouseWithSidebarThemes;
