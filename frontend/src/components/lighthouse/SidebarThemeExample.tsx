import React from 'react';
import { LighthouseWithSidebarThemes } from './LighthouseWithSidebarThemes';

/**
 * 🎨 SIDEBAR THEME SWITCHER EXAMPLE
 * 
 * This is the simplest way to use <PERSON> with the theme switcher
 * integrated into the navigation sidebar.
 * 
 * LOCATION: The theme switcher is in the LEFT SIDEBAR
 * - Look for "Theme" section with colored dots
 * - Located above the Quick Actions buttons
 * - Click to expand and select themes
 */

export function SidebarThemeExample() {
  return (
    <LighthouseWithSidebarThemes 
      defaultTheme="glassmorphism"
    />
  );
}

/**
 * USAGE INSTRUCTIONS:
 * 
 * 1. Import this component:
 *    import { SidebarThemeExample } from '@/components/lighthouse/SidebarThemeExample';
 * 
 * 2. Use it in your app:
 *    <SidebarThemeExample />
 * 
 * 3. Find the theme switcher:
 *    - Look in the LEFT navigation sidebar
 *    - Find the "Theme" section with 3 colored dots
 *    - Click to expand theme options
 *    - Select any theme to switch instantly
 * 
 * 4. Available themes:
 *    - Glassmorphism (modern frosted glass)
 *    - Neumorphism (soft tactile buttons)
 *    - Cyberpunk (neon glows on dark)
 *    - Minimalist (clean content-focused)
 *    - Brutalist (bold high-contrast)
 */

export default SidebarThemeExample;
