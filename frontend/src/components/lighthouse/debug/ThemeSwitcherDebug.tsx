import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { cn } from '~/lib/ui-utils';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';

// Simple debug component to help locate the theme switcher
export function ThemeSwitcherDebug() {
  const [isVisible, setIsVisible] = useState(true);
  const [debugInfo, setDebugInfo] = useState({
    windowWidth: typeof window !== 'undefined' ? window.innerWidth : 0,
    windowHeight: typeof window !== 'undefined' ? window.innerHeight : 0,
    scrollY: typeof window !== 'undefined' ? window.scrollY : 0
  });

  React.useEffect(() => {
    const updateDebugInfo = () => {
      setDebugInfo({
        windowWidth: window.innerWidth,
        windowHeight: window.innerHeight,
        scrollY: window.scrollY
      });
    };

    window.addEventListener('resize', updateDebugInfo);
    window.addEventListener('scroll', updateDebugInfo);

    return () => {
      window.removeEventListener('resize', updateDebugInfo);
      window.removeEventListener('scroll', updateDebugInfo);
    };
  }, []);

  return (
    <>
      {/* Debug Theme Switcher Button - Highly Visible */}
      <div className="fixed top-4 right-4 z-[9999] space-y-2">
        {/* Main Theme Switcher Button */}
        <Button
          onClick={() => alert('Theme Switcher Clicked! This is where it should be.')}
          className="bg-red-500 hover:bg-red-600 text-white font-bold px-4 py-2 rounded-lg shadow-lg border-2 border-white"
          size="sm"
        >
          <div className="flex items-center gap-2">
            <div className="flex gap-1">
              <div className="w-3 h-3 rounded-full bg-blue-500 border border-white" />
              <div className="w-3 h-3 rounded-full bg-purple-500 border border-white" />
              <div className="w-3 h-3 rounded-full bg-cyan-500 border border-white" />
            </div>
            <span>THEMES</span>
          </div>
        </Button>

        {/* Debug Info Panel */}
        {isVisible && (
          <Card className="w-64 bg-yellow-100 border-2 border-yellow-500">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm text-yellow-800">Theme Switcher Debug</CardTitle>
            </CardHeader>
            <CardContent className="text-xs text-yellow-800 space-y-1">
              <div>Position: fixed top-4 right-4</div>
              <div>Z-index: 9999</div>
              <div>Window: {debugInfo.windowWidth}x{debugInfo.windowHeight}</div>
              <div>Scroll: {debugInfo.scrollY}px</div>
              <div className="pt-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setIsVisible(false)}
                  className="text-xs"
                >
                  Hide Debug
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Debug Instructions Overlay */}
      <div className="fixed bottom-4 left-4 z-[9998] max-w-md">
        <Card className="bg-blue-50 border-2 border-blue-500">
          <CardContent className="p-4">
            <h3 className="font-bold text-blue-800 mb-2">🔍 Theme Switcher Location Guide</h3>
            <div className="text-sm text-blue-700 space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-red-500 rounded-full" />
                <span>Look for the RED button in the top-right corner</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full" />
                <span>It should show 3 colored circles + "THEMES"</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full" />
                <span>Position: Fixed top-right (top-4 right-4)</span>
              </div>
              <div className="pt-2 text-xs">
                <strong>If you don't see it:</strong>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>Check if showThemeSwitcher={true}</li>
                  <li>Look for CSS z-index conflicts</li>
                  <li>Verify the component is mounted</li>
                  <li>Check browser console for errors</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Arrows pointing to where theme switcher should be */}
      <div className="fixed top-16 right-16 z-[9997]">
        <div className="text-red-500 text-2xl animate-bounce">↗️</div>
        <div className="text-red-500 text-xs font-bold mt-1">Theme Switcher Should Be Here</div>
      </div>
    </>
  );
}

// Component to test if LighthouseWithThemes is working
export function ThemeSwitcherTest() {
  const [showDebug, setShowDebug] = useState(true);

  return (
    <div className="min-h-screen bg-gray-100 relative">
      {/* Debug overlay */}
      {showDebug && <ThemeSwitcherDebug />}

      {/* Test content */}
      <div className="p-8">
        <Card className="max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle>Theme Switcher Test Page</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>This page helps you locate the theme switcher button.</p>
            
            <div className="space-y-2">
              <h3 className="font-semibold">Expected Location:</h3>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li><strong>Position:</strong> Fixed in top-right corner</li>
                <li><strong>Appearance:</strong> Button with 3 colored circles + "Themes" text</li>
                <li><strong>Behavior:</strong> Opens modal when clicked</li>
                <li><strong>Animation:</strong> Fades in after 1 second delay</li>
              </ul>
            </div>

            <div className="space-y-2">
              <h3 className="font-semibold">Troubleshooting:</h3>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>Check if you're using <code>LighthouseWithThemes</code> component</li>
                <li>Verify <code>showThemeSwitcher={true}</code> prop is set</li>
                <li>Look for the red debug button above (it shows the exact position)</li>
                <li>Check browser console for any JavaScript errors</li>
              </ul>
            </div>

            <div className="pt-4">
              <Button
                onClick={() => setShowDebug(!showDebug)}
                variant="outline"
              >
                {showDebug ? 'Hide' : 'Show'} Debug Overlay
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default ThemeSwitcherDebug;
